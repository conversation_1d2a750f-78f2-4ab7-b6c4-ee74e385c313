const CHUNK_PUBLIC_PATH = "server/app/api/upload/profile-picture/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_0d9ab950._.js");
runtime.loadChunk("server/chunks/node_modules_lodash_cf9f33e9._.js");
runtime.loadChunk("server/chunks/node_modules_cloudinary_64860332._.js");
runtime.loadChunk("server/chunks/node_modules_q_q_8203cf74.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__10f5a2d4._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/upload/profile-picture/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/upload/profile-picture/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/upload/profile-picture/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
