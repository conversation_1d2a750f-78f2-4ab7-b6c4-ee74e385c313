{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/app/api/upload/profile-picture/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { v2 as cloudinary } from 'cloudinary'\n\n// Configure Cloudinary\ncloudinary.config({\n  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,\n  api_key: process.env.CLOUDINARY_API_KEY,\n  api_secret: process.env.CLOUDINARY_API_SECRET,\n})\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData()\n    const file = formData.get('file') as File\n\n    if (!file) {\n      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })\n    }\n\n    // Validate file type\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']\n    if (!allowedTypes.includes(file.type)) {\n      return NextResponse.json(\n        { error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },\n        { status: 400 }\n      )\n    }\n\n    // Validate file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024 // 5MB\n    if (file.size > maxSize) {\n      return NextResponse.json(\n        { error: 'File too large. Maximum size is 5MB.' },\n        { status: 400 }\n      )\n    }\n\n    // Convert file to buffer for Cloudinary upload\n    const bytes = await file.arrayBuffer()\n    const buffer = Buffer.from(bytes)\n\n    // Create unique public_id for Cloudinary\n    const timestamp = Date.now()\n    const randomString = Math.random().toString(36).substring(2, 15)\n    const publicId = `customer_profiles/profile_${timestamp}_${randomString}`\n\n    // Upload to Cloudinary\n    const uploadResult = await new Promise((resolve, reject) => {\n      cloudinary.uploader.upload_stream(\n        {\n          resource_type: 'image',\n          public_id: publicId,\n          folder: 'customer_profiles',\n          transformation: [\n            { width: 400, height: 400, crop: 'fill', gravity: 'face' },\n            { quality: 'auto', fetch_format: 'auto' }\n          ]\n        },\n        (error, result) => {\n          if (error) {\n            reject(error)\n          } else {\n            resolve(result)\n          }\n        }\n      ).end(buffer)\n    })\n\n    const result = uploadResult as any\n\n    return NextResponse.json({\n      success: true,\n      url: result.secure_url,\n      public_id: result.public_id,\n      filename: result.public_id\n    })\n\n  } catch (error) {\n    console.error('Error uploading file to Cloudinary:', error)\n    return NextResponse.json(\n      { error: 'Failed to upload file to cloud storage' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE - Remove profile picture from Cloudinary\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const publicId = searchParams.get('public_id')\n\n    if (!publicId) {\n      return NextResponse.json({ error: 'Public ID is required' }, { status: 400 })\n    }\n\n    // Delete from Cloudinary\n    const result = await cloudinary.uploader.destroy(publicId)\n\n    if (result.result === 'ok') {\n      return NextResponse.json({\n        success: true,\n        message: 'Profile picture deleted successfully from cloud storage'\n      })\n    } else {\n      return NextResponse.json({\n        error: 'Failed to delete image from cloud storage'\n      }, { status: 400 })\n    }\n\n  } catch (error) {\n    console.error('Error deleting file from Cloudinary:', error)\n    return NextResponse.json(\n      { error: 'Failed to delete file from cloud storage' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,uBAAuB;AACvB,0IAAA,CAAA,KAAU,CAAC,MAAM,CAAC;IAChB,YAAY,QAAQ,GAAG,CAAC,qBAAqB;IAC7C,SAAS,QAAQ,GAAG,CAAC,kBAAkB;IACvC,YAAY,QAAQ,GAAG,CAAC,qBAAqB;AAC/C;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,qBAAqB;QACrB,MAAM,eAAe;YAAC;YAAc;YAAa;YAAa;SAAa;QAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2D,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,UAAU,IAAI,OAAO,KAAK,MAAM;;QACtC,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuC,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,+CAA+C;QAC/C,MAAM,QAAQ,MAAM,KAAK,WAAW;QACpC,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,yCAAyC;QACzC,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,eAAe,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;QAC7D,MAAM,WAAW,CAAC,0BAA0B,EAAE,UAAU,CAAC,EAAE,cAAc;QAEzE,uBAAuB;QACvB,MAAM,eAAe,MAAM,IAAI,QAAQ,CAAC,SAAS;YAC/C,0IAAA,CAAA,KAAU,CAAC,QAAQ,CAAC,aAAa,CAC/B;gBACE,eAAe;gBACf,WAAW;gBACX,QAAQ;gBACR,gBAAgB;oBACd;wBAAE,OAAO;wBAAK,QAAQ;wBAAK,MAAM;wBAAQ,SAAS;oBAAO;oBACzD;wBAAE,SAAS;wBAAQ,cAAc;oBAAO;iBACzC;YACH,GACA,CAAC,OAAO;gBACN,IAAI,OAAO;oBACT,OAAO;gBACT,OAAO;oBACL,QAAQ;gBACV;YACF,GACA,GAAG,CAAC;QACR;QAEA,MAAM,SAAS;QAEf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,KAAK,OAAO,UAAU;YACtB,WAAW,OAAO,SAAS;YAC3B,UAAU,OAAO,SAAS;QAC5B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyC,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAElC,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAwB,GAAG;gBAAE,QAAQ;YAAI;QAC7E;QAEA,yBAAyB;QACzB,MAAM,SAAS,MAAM,0IAAA,CAAA,KAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;QAEjD,IAAI,OAAO,MAAM,KAAK,MAAM;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX;QACF,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2C,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}