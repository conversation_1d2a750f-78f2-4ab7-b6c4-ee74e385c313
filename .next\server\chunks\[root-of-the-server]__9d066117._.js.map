{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/lib/env.ts"], "sourcesContent": ["// Environment variables validation and configuration\n\nimport { z } from 'zod'\n\n// Define the schema for environment variables\nconst envSchema = z.object({\n  // Node environment\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\n  \n  // Supabase configuration\n  NEXT_PUBLIC_SUPABASE_URL: z.string().optional(),\n  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().optional(),\n  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),\n  \n  // Cloudinary configuration\n  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: z.string().optional(),\n  CLOUDINARY_API_KEY: z.string().optional(),\n  CLOUDINARY_API_SECRET: z.string().optional(),\n\n  // Google Gemini AI configuration\n  GEMINI_API_KEY: z.string().optional(),\n  \n  // Authentication (optional for build time)\n  NEXTAUTH_SECRET: z.string().optional(),\n  NEXTAUTH_URL: z.string().optional(),\n  \n  // Optional configuration\n  DEBUG: z.string().transform(val => val === 'true').default('false'),\n})\n\n// Parse and validate environment variables\nfunction validateEnv() {\n  try {\n    return envSchema.parse(process.env)\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)\n      throw new Error(\n        `❌ Invalid environment variables:\\n${missingVars.join('\\n')}\\n\\n` +\n        `Please check your .env.local file and ensure all required variables are set.\\n` +\n        `See .env.example for reference.`\n      )\n    }\n    throw error\n  }\n}\n\n// Export validated environment variables\nexport const env = validateEnv()\n\n// Environment-specific configurations\nexport const config = {\n  isDevelopment: env.NODE_ENV === 'development',\n  isProduction: env.NODE_ENV === 'production',\n  isTest: env.NODE_ENV === 'test',\n  \n  // Database\n  database: {\n    url: env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',\n    anonKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key',\n    serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,\n  },\n\n  // File storage\n  cloudinary: {\n    cloudName: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'placeholder',\n    apiKey: env.CLOUDINARY_API_KEY,\n    apiSecret: env.CLOUDINARY_API_SECRET,\n  },\n\n  // AI configuration\n  ai: {\n    geminiApiKey: env.GEMINI_API_KEY,\n  },\n  \n  // Authentication\n  auth: {\n    secret: env.NEXTAUTH_SECRET,\n    url: env.NEXTAUTH_URL,\n  },\n  \n  // Debug mode\n  debug: env.DEBUG,\n} as const\n\n// Runtime environment checks\nexport function checkRequiredEnvVars() {\n  const requiredVars = [\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',\n  ]\n  \n  const missingVars = requiredVars.filter(varName => !process.env[varName])\n  \n  if (missingVars.length > 0) {\n    throw new Error(\n      `❌ Missing required environment variables: ${missingVars.join(', ')}\\n` +\n      `Please check your .env.local file.`\n    )\n  }\n}\n\n// Development-only environment checks\nexport function checkDevelopmentEnvVars() {\n  if (config.isDevelopment) {\n    const devVars = [\n      'CLOUDINARY_API_KEY',\n      'CLOUDINARY_API_SECRET',\n      'NEXTAUTH_SECRET',\n    ]\n    \n    const missingDevVars = devVars.filter(varName => !process.env[varName])\n    \n    if (missingDevVars.length > 0) {\n      console.warn(\n        `⚠️  Missing development environment variables: ${missingDevVars.join(', ')}\\n` +\n        `Some features may not work properly.`\n      )\n    }\n  }\n}\n\n// Production-only environment checks\nexport function checkProductionEnvVars() {\n  if (config.isProduction) {\n    const prodVars = [\n      'SUPABASE_SERVICE_ROLE_KEY',\n      'CLOUDINARY_API_KEY',\n      'CLOUDINARY_API_SECRET',\n      'NEXTAUTH_SECRET',\n      'NEXTAUTH_URL',\n    ]\n    \n    const missingProdVars = prodVars.filter(varName => !process.env[varName])\n    \n    if (missingProdVars.length > 0) {\n      throw new Error(\n        `❌ Missing production environment variables: ${missingProdVars.join(', ')}\\n` +\n        `These are required for production deployment.`\n      )\n    }\n  }\n}\n\n// Initialize environment validation\nexport function initializeEnv() {\n  try {\n    checkRequiredEnvVars()\n    checkDevelopmentEnvVars()\n    checkProductionEnvVars()\n    \n    if (config.debug) {\n      console.warn('✅ Environment variables validated successfully')\n      console.warn('📊 Configuration:', {\n        environment: env.NODE_ENV,\n        database: !!config.database.url,\n        cloudinary: !!config.cloudinary.cloudName,\n        auth: !!config.auth.secret,\n      })\n    }\n  } catch (error) {\n    console.error(error)\n    if (config.isProduction) {\n      process.exit(1)\n    }\n  }\n}\n\n// Export individual environment variables for convenience\nexport const {\n  NODE_ENV,\n  NEXT_PUBLIC_SUPABASE_URL,\n  NEXT_PUBLIC_SUPABASE_ANON_KEY,\n  SUPABASE_SERVICE_ROLE_KEY,\n  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,\n  CLOUDINARY_API_KEY,\n  CLOUDINARY_API_SECRET,\n  NEXTAUTH_SECRET,\n  NEXTAUTH_URL,\n  DEBUG,\n  GEMINI_API_KEY,\n} = env\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;;;;;;;;;;;AAErD;;AAEA,8CAA8C;AAC9C,MAAM,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,mBAAmB;IACnB,UAAU,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAEhE,yBAAyB;IACzB,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7C,+BAA+B,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClD,2BAA2B,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE9C,2BAA2B;IAC3B,mCAAmC,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACtD,oBAAoB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvC,uBAAuB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE1C,iCAAiC;IACjC,gBAAgB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAEnC,2CAA2C;IAC3C,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAEjC,yBAAyB;IACzB,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAA,MAAO,QAAQ,QAAQ,OAAO,CAAC;AAC7D;AAEA,2CAA2C;AAC3C,SAAS;IACP,IAAI;QACF,OAAO,UAAU,KAAK,CAAC,QAAQ,GAAG;IACpC,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oKAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,cAAc,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YACnF,MAAM,IAAI,MACR,CAAC,kCAAkC,EAAE,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC,GACjE,CAAC,8EAA8E,CAAC,GAChF,CAAC,+BAA+B,CAAC;QAErC;QACA,MAAM;IACR;AACF;AAGO,MAAM,MAAM;AAGZ,MAAM,SAAS;IACpB,eAAe,IAAI,QAAQ,KAAK;IAChC,cAAc,IAAI,QAAQ,KAAK;IAC/B,QAAQ,IAAI,QAAQ,KAAK;IAEzB,WAAW;IACX,UAAU;QACR,KAAK,IAAI,wBAAwB,IAAI;QACrC,SAAS,IAAI,6BAA6B,IAAI;QAC9C,gBAAgB,IAAI,yBAAyB;IAC/C;IAEA,eAAe;IACf,YAAY;QACV,WAAW,IAAI,iCAAiC,IAAI;QACpD,QAAQ,IAAI,kBAAkB;QAC9B,WAAW,IAAI,qBAAqB;IACtC;IAEA,mBAAmB;IACnB,IAAI;QACF,cAAc,IAAI,cAAc;IAClC;IAEA,iBAAiB;IACjB,MAAM;QACJ,QAAQ,IAAI,eAAe;QAC3B,KAAK,IAAI,YAAY;IACvB;IAEA,aAAa;IACb,OAAO,IAAI,KAAK;AAClB;AAGO,SAAS;IACd,MAAM,eAAe;QACnB;QACA;QACA;KACD;IAED,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;IAExE,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,IAAI,MACR,CAAC,0CAA0C,EAAE,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC,GACvE,CAAC,kCAAkC,CAAC;IAExC;AACF;AAGO,SAAS;IACd,IAAI,OAAO,aAAa,EAAE;QACxB,MAAM,UAAU;YACd;YACA;YACA;SACD;QAED,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;QAEtE,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,QAAQ,IAAI,CACV,CAAC,+CAA+C,EAAE,eAAe,IAAI,CAAC,MAAM,EAAE,CAAC,GAC/E,CAAC,oCAAoC,CAAC;QAE1C;IACF;AACF;AAGO,SAAS;IACd,IAAI,OAAO,YAAY,EAAE;QACvB,MAAM,WAAW;YACf;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;QAExE,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,IAAI,MACR,CAAC,4CAA4C,EAAE,gBAAgB,IAAI,CAAC,MAAM,EAAE,CAAC,GAC7E,CAAC,6CAA6C,CAAC;QAEnD;IACF;AACF;AAGO,SAAS;IACd,IAAI;QACF;QACA;QACA;QAEA,IAAI,OAAO,KAAK,EAAE;YAChB,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC,qBAAqB;gBAChC,aAAa,IAAI,QAAQ;gBACzB,UAAU,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;gBAC/B,YAAY,CAAC,CAAC,OAAO,UAAU,CAAC,SAAS;gBACzC,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM;YAC5B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,IAAI,OAAO,YAAY,EAAE;YACvB,QAAQ,IAAI,CAAC;QACf;IACF;AACF;AAGO,MAAM,EACX,QAAQ,EACR,wBAAwB,EACxB,6BAA6B,EAC7B,yBAAyB,EACzB,iCAAiC,EACjC,kBAAkB,EAClB,qBAAqB,EACrB,eAAe,EACf,YAAY,EACZ,KAAK,EACL,cAAc,EACf,GAAG", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { config } from './env'\n\n// Create Supabase client with validated environment variables\nexport const supabase = createClient(\n  config.database.url,\n  config.database.anonKey,\n  {\n    auth: {\n      autoRefreshToken: true,\n      persistSession: true,\n      detectSessionInUrl: true\n    },\n    db: {\n      schema: 'public'\n    },\n    global: {\n      headers: {\n        'X-Client-Info': 'revantad-store@1.0.0'\n      }\n    }\n  }\n)\n\n// Database Types\nexport interface Product {\n  id: string\n  name: string\n  image_url?: string\n  net_weight: string\n  price: number\n  stock_quantity: number\n  category: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Customer {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  profile_picture_url?: string\n  phone_number?: string\n  address?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerDebt {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  product_name: string\n  product_price: number\n  quantity: number\n  total_amount: number\n  debt_date: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerPayment {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  payment_amount: number\n  payment_date: string\n  payment_method: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerBalance {\n  customer_name: string\n  customer_family_name: string\n  total_debt: number\n  total_payments: number\n  remaining_balance: number\n  last_debt_date?: string\n  last_payment_date?: string\n}\n\n// Product Categories\nexport const PRODUCT_CATEGORIES = [\n  'Snacks',\n  'Canned Goods',\n  'Beverages',\n  'Personal Care',\n  'Household Items',\n  'Condiments',\n  'Rice & Grains',\n  'Instant Foods',\n  'Dairy Products',\n  'Others'\n] as const\n\nexport type ProductCategory = typeof PRODUCT_CATEGORIES[number]\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EACjC,mHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,GAAG,EACnB,mHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,OAAO,EACvB;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,IAAI;QACF,QAAQ;IACV;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;QACnB;IACF;AACF;AAgEK,MAAM,qBAAqB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/app/api/products/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nimport { supabase } from '@/lib/supabase'\n\n// GET - Fetch single product\nexport async function GET(\n  _request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n    const { data: product, error } = await supabase\n      .from('products')\n      .select('*')\n      .eq('id', id)\n      .single()\n\n    if (error) {\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    if (!product) {\n      return NextResponse.json({ error: 'Product not found' }, { status: 404 })\n    }\n\n    return NextResponse.json({ product })\n  } catch {\n    return NextResponse.json(\n      { error: 'Failed to fetch product' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT - Update product\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n    const body = await request.json()\n    const { name, image_url, net_weight, price, stock_quantity, category } = body\n\n    // Validate required fields\n    if (!name || !net_weight || !price || !category) {\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      )\n    }\n\n    const { data: product, error } = await supabase\n      .from('products')\n      .update({\n        name,\n        image_url,\n        net_weight,\n        price: parseFloat(price),\n        stock_quantity: parseInt(stock_quantity) || 0,\n        category,\n      })\n      .eq('id', id)\n      .select()\n      .single()\n\n    if (error) {\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ product })\n  } catch {\n    return NextResponse.json(\n      { error: 'Failed to update product' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE - Delete product\nexport async function DELETE(\n  _request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n    const { error } = await supabase\n      .from('products')\n      .delete()\n      .eq('id', id)\n\n    if (error) {\n      return NextResponse.json({ error: error.message }, { status: 500 })\n    }\n\n    return NextResponse.json({ message: 'Product deleted successfully' })\n  } catch {\n    return NextResponse.json(\n      { error: 'Failed to delete product' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;AAGO,eAAe,IACpB,QAAqB,EACrB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAQ;IACrC,EAAE,OAAM;QACN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG;QAEzE,2BAA2B;QAC3B,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,UAAU;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,YACL,MAAM,CAAC;YACN;YACA;YACA;YACA,OAAO,WAAW;YAClB,gBAAgB,SAAS,mBAAmB;YAC5C;QACF,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAQ;IACrC,EAAE,OAAM;QACN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,QAAqB,EACrB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,MAAM,OAAO;YAAC,GAAG;gBAAE,QAAQ;YAAI;QACnE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAA+B;IACrE,EAAE,OAAM;QACN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}