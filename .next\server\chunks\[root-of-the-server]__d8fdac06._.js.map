{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/lib/api-utils.ts"], "sourcesContent": ["// API utilities for consistent error handling and response formatting\n\nimport { NextResponse } from 'next/server'\nimport type { ApiResponse } from '@/types'\n\n// HTTP status codes\nexport const HTTP_STATUS = {\n  OK: 200,\n  CREATED: 201,\n  BAD_REQUEST: 400,\n  UNAUTHORIZED: 401,\n  FORBIDDEN: 403,\n  NOT_FOUND: 404,\n  METHOD_NOT_ALLOWED: 405,\n  CONFLICT: 409,\n  UNPROCESSABLE_ENTITY: 422,\n  INTERNAL_SERVER_ERROR: 500,\n} as const\n\n// Error types\nexport class ApiError extends Error {\n  constructor(\n    public message: string,\n    public statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR,\n    public code?: string\n  ) {\n    super(message)\n    this.name = 'ApiError'\n  }\n}\n\n// Success response helper\nexport function successResponse<T>(\n  data: T,\n  message?: string,\n  statusCode: number = HTTP_STATUS.OK\n): NextResponse<ApiResponse<T>> {\n  const response: ApiResponse<T> = {\n    success: true,\n    data,\n  }\n\n  if (message) {\n    response.message = message\n  }\n\n  return NextResponse.json(response, { status: statusCode })\n}\n\n// Error response helper\nexport function errorResponse(\n  message: string,\n  statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR,\n  code?: string\n): NextResponse<ApiResponse> {\n  return NextResponse.json(\n    {\n      success: false,\n      error: message,\n      code,\n    },\n    { status: statusCode }\n  )\n}\n\n// Validation error response\nexport function validationErrorResponse(\n  errors: Record<string, string[]>\n): NextResponse<ApiResponse> {\n  return NextResponse.json(\n    {\n      success: false,\n      error: 'Validation failed',\n      data: errors,\n    },\n    { status: HTTP_STATUS.UNPROCESSABLE_ENTITY }\n  )\n}\n\n// Method not allowed response\nexport function methodNotAllowedResponse(\n  allowedMethods: string[]\n): NextResponse<ApiResponse> {\n  return NextResponse.json(\n    {\n      success: false,\n      error: `Method not allowed. Allowed methods: ${allowedMethods.join(', ')}`,\n    },\n    { \n      status: HTTP_STATUS.METHOD_NOT_ALLOWED,\n      headers: {\n        Allow: allowedMethods.join(', ')\n      }\n    }\n  )\n}\n\n// Async error handler wrapper\nexport function withErrorHandler<T extends any[], R>(\n  handler: (...args: T) => Promise<R>\n) {\n  return async (...args: T): Promise<R | NextResponse<ApiResponse>> => {\n    try {\n      return await handler(...args)\n    } catch (error) {\n      console.error('API Error:', error)\n      \n      if (error instanceof ApiError) {\n        return errorResponse(error.message, error.statusCode, error.code)\n      }\n      \n      if (error instanceof Error) {\n        return errorResponse(error.message)\n      }\n      \n      return errorResponse('An unexpected error occurred')\n    }\n  }\n}\n\n// Request validation helpers\nexport async function validateRequestBody<T>(\n  request: Request,\n  validator: (data: any) => T\n): Promise<T> {\n  try {\n    const body = await request.json()\n    return validator(body)\n  } catch {\n    throw new ApiError('Invalid request body', HTTP_STATUS.BAD_REQUEST)\n  }\n}\n\nexport function validateRequiredFields<T extends Record<string, any>>(\n  data: T,\n  requiredFields: (keyof T)[]\n): void {\n  const missingFields = requiredFields.filter(field => \n    data[field] === undefined || data[field] === null || data[field] === ''\n  )\n  \n  if (missingFields.length > 0) {\n    throw new ApiError(\n      `Missing required fields: ${missingFields.join(', ')}`,\n      HTTP_STATUS.BAD_REQUEST\n    )\n  }\n}\n\n// Database error handler\nexport function handleDatabaseError(error: any): never {\n  console.error('Database error:', error)\n  \n  // Handle specific database errors\n  if (error.code === '23505') { // Unique constraint violation\n    throw new ApiError('Resource already exists', HTTP_STATUS.CONFLICT)\n  }\n  \n  if (error.code === '23503') { // Foreign key constraint violation\n    throw new ApiError('Referenced resource not found', HTTP_STATUS.BAD_REQUEST)\n  }\n  \n  if (error.code === '23502') { // Not null constraint violation\n    throw new ApiError('Required field is missing', HTTP_STATUS.BAD_REQUEST)\n  }\n  \n  // Generic database error\n  throw new ApiError('Database operation failed', HTTP_STATUS.INTERNAL_SERVER_ERROR)\n}\n\n// Pagination helpers\nexport interface PaginationOptions {\n  page?: number\n  limit?: number\n  maxLimit?: number\n}\n\nexport function parsePaginationParams(\n  searchParams: URLSearchParams,\n  options: PaginationOptions = {}\n): { page: number; limit: number; offset: number } {\n  const { maxLimit = 100 } = options\n  \n  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10))\n  const limit = Math.min(\n    maxLimit,\n    Math.max(1, parseInt(searchParams.get('limit') || '10', 10))\n  )\n  const offset = (page - 1) * limit\n  \n  return { page, limit, offset }\n}\n\n// CORS headers for API routes\nexport const corsHeaders = {\n  'Access-Control-Allow-Origin': '*',\n  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n  'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n}\n\n// Handle CORS preflight requests\nexport function handleCorsPreflightRequest(): NextResponse {\n  return new NextResponse(null, {\n    status: 200,\n    headers: corsHeaders,\n  })\n}\n"], "names": [], "mappings": "AAAA,sEAAsE;;;;;;;;;;;;;;;;AAEtE;;AAIO,MAAM,cAAc;IACzB,IAAI;IACJ,SAAS;IACT,aAAa;IACb,cAAc;IACd,WAAW;IACX,WAAW;IACX,oBAAoB;IACpB,UAAU;IACV,sBAAsB;IACtB,uBAAuB;AACzB;AAGO,MAAM,iBAAiB;;;;IAC5B,YACE,AAAO,OAAe,EACtB,AAAO,aAAqB,YAAY,qBAAqB,EAC7D,AAAO,IAAa,CACpB;QACA,KAAK,CAAC,eAJC,UAAA,cACA,aAAA,iBACA,OAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,SAAS,gBACd,IAAO,EACP,OAAgB,EAChB,aAAqB,YAAY,EAAE;IAEnC,MAAM,WAA2B;QAC/B,SAAS;QACT;IACF;IAEA,IAAI,SAAS;QACX,SAAS,OAAO,GAAG;IACrB;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;QAAE,QAAQ;IAAW;AAC1D;AAGO,SAAS,cACd,OAAe,EACf,aAAqB,YAAY,qBAAqB,EACtD,IAAa;IAEb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;QACP;IACF,GACA;QAAE,QAAQ;IAAW;AAEzB;AAGO,SAAS,wBACd,MAAgC;IAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO;QACP,MAAM;IACR,GACA;QAAE,QAAQ,YAAY,oBAAoB;IAAC;AAE/C;AAGO,SAAS,yBACd,cAAwB;IAExB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT,OAAO,CAAC,qCAAqC,EAAE,eAAe,IAAI,CAAC,OAAO;IAC5E,GACA;QACE,QAAQ,YAAY,kBAAkB;QACtC,SAAS;YACP,OAAO,eAAe,IAAI,CAAC;QAC7B;IACF;AAEJ;AAGO,SAAS,iBACd,OAAmC;IAEnC,OAAO,OAAO,GAAG;QACf,IAAI;YACF,OAAO,MAAM,WAAW;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAE5B,IAAI,iBAAiB,UAAU;gBAC7B,OAAO,cAAc,MAAM,OAAO,EAAE,MAAM,UAAU,EAAE,MAAM,IAAI;YAClE;YAEA,IAAI,iBAAiB,OAAO;gBAC1B,OAAO,cAAc,MAAM,OAAO;YACpC;YAEA,OAAO,cAAc;QACvB;IACF;AACF;AAGO,eAAe,oBACpB,OAAgB,EAChB,SAA2B;IAE3B,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,OAAO,UAAU;IACnB,EAAE,OAAM;QACN,MAAM,IAAI,SAAS,wBAAwB,YAAY,WAAW;IACpE;AACF;AAEO,SAAS,uBACd,IAAO,EACP,cAA2B;IAE3B,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,QAC1C,IAAI,CAAC,MAAM,KAAK,aAAa,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,KAAK;IAGvE,IAAI,cAAc,MAAM,GAAG,GAAG;QAC5B,MAAM,IAAI,SACR,CAAC,yBAAyB,EAAE,cAAc,IAAI,CAAC,OAAO,EACtD,YAAY,WAAW;IAE3B;AACF;AAGO,SAAS,oBAAoB,KAAU;IAC5C,QAAQ,KAAK,CAAC,mBAAmB;IAEjC,kCAAkC;IAClC,IAAI,MAAM,IAAI,KAAK,SAAS;QAC1B,MAAM,IAAI,SAAS,2BAA2B,YAAY,QAAQ;IACpE;IAEA,IAAI,MAAM,IAAI,KAAK,SAAS;QAC1B,MAAM,IAAI,SAAS,iCAAiC,YAAY,WAAW;IAC7E;IAEA,IAAI,MAAM,IAAI,KAAK,SAAS;QAC1B,MAAM,IAAI,SAAS,6BAA6B,YAAY,WAAW;IACzE;IAEA,yBAAyB;IACzB,MAAM,IAAI,SAAS,6BAA6B,YAAY,qBAAqB;AACnF;AASO,SAAS,sBACd,YAA6B,EAC7B,UAA6B,CAAC,CAAC;IAE/B,MAAM,EAAE,WAAW,GAAG,EAAE,GAAG;IAE3B,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,SAAS,aAAa,GAAG,CAAC,WAAW,KAAK;IACnE,MAAM,QAAQ,KAAK,GAAG,CACpB,UACA,KAAK,GAAG,CAAC,GAAG,SAAS,aAAa,GAAG,CAAC,YAAY,MAAM;IAE1D,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;IAE5B,OAAO;QAAE;QAAM;QAAO;IAAO;AAC/B;AAGO,MAAM,cAAc;IACzB,+BAA+B;IAC/B,gCAAgC;IAChC,gCAAgC;AAClC;AAGO,SAAS;IACd,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/lib/env.ts"], "sourcesContent": ["// Environment variables validation and configuration\n\nimport { z } from 'zod'\n\n// Define the schema for environment variables\nconst envSchema = z.object({\n  // Node environment\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\n  \n  // Supabase configuration\n  NEXT_PUBLIC_SUPABASE_URL: z.string().optional(),\n  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().optional(),\n  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),\n  \n  // Cloudinary configuration\n  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: z.string().optional(),\n  CLOUDINARY_API_KEY: z.string().optional(),\n  CLOUDINARY_API_SECRET: z.string().optional(),\n\n  // Google Gemini AI configuration\n  GEMINI_API_KEY: z.string().optional(),\n  \n  // Authentication (optional for build time)\n  NEXTAUTH_SECRET: z.string().optional(),\n  NEXTAUTH_URL: z.string().optional(),\n  \n  // Optional configuration\n  DEBUG: z.string().transform(val => val === 'true').default('false'),\n})\n\n// Parse and validate environment variables\nfunction validateEnv() {\n  try {\n    return envSchema.parse(process.env)\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)\n      throw new Error(\n        `❌ Invalid environment variables:\\n${missingVars.join('\\n')}\\n\\n` +\n        `Please check your .env.local file and ensure all required variables are set.\\n` +\n        `See .env.example for reference.`\n      )\n    }\n    throw error\n  }\n}\n\n// Export validated environment variables\nexport const env = validateEnv()\n\n// Environment-specific configurations\nexport const config = {\n  isDevelopment: env.NODE_ENV === 'development',\n  isProduction: env.NODE_ENV === 'production',\n  isTest: env.NODE_ENV === 'test',\n  \n  // Database\n  database: {\n    url: env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',\n    anonKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key',\n    serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,\n  },\n\n  // File storage\n  cloudinary: {\n    cloudName: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'placeholder',\n    apiKey: env.CLOUDINARY_API_KEY,\n    apiSecret: env.CLOUDINARY_API_SECRET,\n  },\n\n  // AI configuration\n  ai: {\n    geminiApiKey: env.GEMINI_API_KEY,\n  },\n  \n  // Authentication\n  auth: {\n    secret: env.NEXTAUTH_SECRET,\n    url: env.NEXTAUTH_URL,\n  },\n  \n  // Debug mode\n  debug: env.DEBUG,\n} as const\n\n// Runtime environment checks\nexport function checkRequiredEnvVars() {\n  const requiredVars = [\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',\n  ]\n  \n  const missingVars = requiredVars.filter(varName => !process.env[varName])\n  \n  if (missingVars.length > 0) {\n    throw new Error(\n      `❌ Missing required environment variables: ${missingVars.join(', ')}\\n` +\n      `Please check your .env.local file.`\n    )\n  }\n}\n\n// Development-only environment checks\nexport function checkDevelopmentEnvVars() {\n  if (config.isDevelopment) {\n    const devVars = [\n      'CLOUDINARY_API_KEY',\n      'CLOUDINARY_API_SECRET',\n      'NEXTAUTH_SECRET',\n    ]\n    \n    const missingDevVars = devVars.filter(varName => !process.env[varName])\n    \n    if (missingDevVars.length > 0) {\n      console.warn(\n        `⚠️  Missing development environment variables: ${missingDevVars.join(', ')}\\n` +\n        `Some features may not work properly.`\n      )\n    }\n  }\n}\n\n// Production-only environment checks\nexport function checkProductionEnvVars() {\n  if (config.isProduction) {\n    const prodVars = [\n      'SUPABASE_SERVICE_ROLE_KEY',\n      'CLOUDINARY_API_KEY',\n      'CLOUDINARY_API_SECRET',\n      'NEXTAUTH_SECRET',\n      'NEXTAUTH_URL',\n    ]\n    \n    const missingProdVars = prodVars.filter(varName => !process.env[varName])\n    \n    if (missingProdVars.length > 0) {\n      throw new Error(\n        `❌ Missing production environment variables: ${missingProdVars.join(', ')}\\n` +\n        `These are required for production deployment.`\n      )\n    }\n  }\n}\n\n// Initialize environment validation\nexport function initializeEnv() {\n  try {\n    checkRequiredEnvVars()\n    checkDevelopmentEnvVars()\n    checkProductionEnvVars()\n    \n    if (config.debug) {\n      console.warn('✅ Environment variables validated successfully')\n      console.warn('📊 Configuration:', {\n        environment: env.NODE_ENV,\n        database: !!config.database.url,\n        cloudinary: !!config.cloudinary.cloudName,\n        auth: !!config.auth.secret,\n      })\n    }\n  } catch (error) {\n    console.error(error)\n    if (config.isProduction) {\n      process.exit(1)\n    }\n  }\n}\n\n// Export individual environment variables for convenience\nexport const {\n  NODE_ENV,\n  NEXT_PUBLIC_SUPABASE_URL,\n  NEXT_PUBLIC_SUPABASE_ANON_KEY,\n  SUPABASE_SERVICE_ROLE_KEY,\n  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,\n  CLOUDINARY_API_KEY,\n  CLOUDINARY_API_SECRET,\n  NEXTAUTH_SECRET,\n  NEXTAUTH_URL,\n  DEBUG,\n  GEMINI_API_KEY,\n} = env\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;;;;;;;;;;;AAErD;;AAEA,8CAA8C;AAC9C,MAAM,YAAY,oKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,mBAAmB;IACnB,UAAU,oKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAEhE,yBAAyB;IACzB,0BAA0B,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7C,+BAA+B,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClD,2BAA2B,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE9C,2BAA2B;IAC3B,mCAAmC,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACtD,oBAAoB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvC,uBAAuB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE1C,iCAAiC;IACjC,gBAAgB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAEnC,2CAA2C;IAC3C,iBAAiB,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,cAAc,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAEjC,yBAAyB;IACzB,OAAO,oKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAA,MAAO,QAAQ,QAAQ,OAAO,CAAC;AAC7D;AAEA,2CAA2C;AAC3C,SAAS;IACP,IAAI;QACF,OAAO,UAAU,KAAK,CAAC,QAAQ,GAAG;IACpC,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,oKAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,cAAc,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YACnF,MAAM,IAAI,MACR,CAAC,kCAAkC,EAAE,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC,GACjE,CAAC,8EAA8E,CAAC,GAChF,CAAC,+BAA+B,CAAC;QAErC;QACA,MAAM;IACR;AACF;AAGO,MAAM,MAAM;AAGZ,MAAM,SAAS;IACpB,eAAe,IAAI,QAAQ,KAAK;IAChC,cAAc,IAAI,QAAQ,KAAK;IAC/B,QAAQ,IAAI,QAAQ,KAAK;IAEzB,WAAW;IACX,UAAU;QACR,KAAK,IAAI,wBAAwB,IAAI;QACrC,SAAS,IAAI,6BAA6B,IAAI;QAC9C,gBAAgB,IAAI,yBAAyB;IAC/C;IAEA,eAAe;IACf,YAAY;QACV,WAAW,IAAI,iCAAiC,IAAI;QACpD,QAAQ,IAAI,kBAAkB;QAC9B,WAAW,IAAI,qBAAqB;IACtC;IAEA,mBAAmB;IACnB,IAAI;QACF,cAAc,IAAI,cAAc;IAClC;IAEA,iBAAiB;IACjB,MAAM;QACJ,QAAQ,IAAI,eAAe;QAC3B,KAAK,IAAI,YAAY;IACvB;IAEA,aAAa;IACb,OAAO,IAAI,KAAK;AAClB;AAGO,SAAS;IACd,MAAM,eAAe;QACnB;QACA;QACA;KACD;IAED,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;IAExE,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,IAAI,MACR,CAAC,0CAA0C,EAAE,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC,GACvE,CAAC,kCAAkC,CAAC;IAExC;AACF;AAGO,SAAS;IACd,IAAI,OAAO,aAAa,EAAE;QACxB,MAAM,UAAU;YACd;YACA;YACA;SACD;QAED,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;QAEtE,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,QAAQ,IAAI,CACV,CAAC,+CAA+C,EAAE,eAAe,IAAI,CAAC,MAAM,EAAE,CAAC,GAC/E,CAAC,oCAAoC,CAAC;QAE1C;IACF;AACF;AAGO,SAAS;IACd,IAAI,OAAO,YAAY,EAAE;QACvB,MAAM,WAAW;YACf;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;QAExE,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,IAAI,MACR,CAAC,4CAA4C,EAAE,gBAAgB,IAAI,CAAC,MAAM,EAAE,CAAC,GAC7E,CAAC,6CAA6C,CAAC;QAEnD;IACF;AACF;AAGO,SAAS;IACd,IAAI;QACF;QACA;QACA;QAEA,IAAI,OAAO,KAAK,EAAE;YAChB,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC,qBAAqB;gBAChC,aAAa,IAAI,QAAQ;gBACzB,UAAU,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;gBAC/B,YAAY,CAAC,CAAC,OAAO,UAAU,CAAC,SAAS;gBACzC,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM;YAC5B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,IAAI,OAAO,YAAY,EAAE;YACvB,QAAQ,IAAI,CAAC;QACf;IACF;AACF;AAGO,MAAM,EACX,QAAQ,EACR,wBAAwB,EACxB,6BAA6B,EAC7B,yBAAyB,EACzB,iCAAiC,EACjC,kBAAkB,EAClB,qBAAqB,EACrB,eAAe,EACf,YAAY,EACZ,KAAK,EACL,cAAc,EACf,GAAG", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { config } from './env'\n\n// Create Supabase client with validated environment variables\nexport const supabase = createClient(\n  config.database.url,\n  config.database.anonKey,\n  {\n    auth: {\n      autoRefreshToken: true,\n      persistSession: true,\n      detectSessionInUrl: true\n    },\n    db: {\n      schema: 'public'\n    },\n    global: {\n      headers: {\n        'X-Client-Info': 'revantad-store@1.0.0'\n      }\n    }\n  }\n)\n\n// Database Types\nexport interface Product {\n  id: string\n  name: string\n  image_url?: string\n  net_weight: string\n  price: number\n  stock_quantity: number\n  category: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Customer {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  profile_picture_url?: string\n  phone_number?: string\n  address?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerDebt {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  product_name: string\n  product_price: number\n  quantity: number\n  total_amount: number\n  debt_date: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerPayment {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  payment_amount: number\n  payment_date: string\n  payment_method: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerBalance {\n  customer_name: string\n  customer_family_name: string\n  total_debt: number\n  total_payments: number\n  remaining_balance: number\n  last_debt_date?: string\n  last_payment_date?: string\n}\n\n// Product Categories\nexport const PRODUCT_CATEGORIES = [\n  'Snacks',\n  'Canned Goods',\n  'Beverages',\n  'Personal Care',\n  'Household Items',\n  'Condiments',\n  'Rice & Grains',\n  'Instant Foods',\n  'Dairy Products',\n  'Others'\n] as const\n\nexport type ProductCategory = typeof PRODUCT_CATEGORIES[number]\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EACjC,mHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,GAAG,EACnB,mHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,OAAO,EACvB;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,IAAI;QACF,QAAQ;IACV;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;QACnB;IACF;AACF;AAgEK,MAAM,qBAAqB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\n\nimport {\n  successResponse,\n  errorResponse,\n  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  validateRequestBody,\n  validateRequired<PERSON>ields,\n  handleDatabaseError,\n  parsePaginationParams,\n  handleCorsPreflightRequest\n} from '@/lib/api-utils'\nimport { supabase } from '@/lib/supabase'\n\n\n// Handle CORS preflight requests\nexport async function OPTIONS() {\n  return handleCorsPreflightRequest()\n}\n\n// GET - Fetch all products with pagination and filtering\nexport const GET = withErrorHandler(async (request: NextRequest) => {\n  const { searchParams } = new URL(request.url)\n  const { page, limit, offset } = parsePaginationParams(searchParams)\n\n  // Optional filters\n  const category = searchParams.get('category')\n  const search = searchParams.get('search')\n  const lowStock = searchParams.get('lowStock') === 'true'\n\n  let query = supabase\n    .from('products')\n    .select('*', { count: 'exact' })\n    .order('created_at', { ascending: false })\n    .range(offset, offset + limit - 1)\n\n  // Apply filters\n  if (category) {\n    query = query.eq('category', category)\n  }\n\n  if (search) {\n    query = query.ilike('name', `%${search}%`)\n  }\n\n  if (lowStock) {\n    query = query.lt('stock_quantity', 10)\n  }\n\n  const { data: products, error, count } = await query\n\n  if (error) {\n    handleDatabaseError(error)\n  }\n\n  return successResponse({\n    products: products || [],\n    pagination: {\n      page,\n      limit,\n      total: count || 0,\n      totalPages: Math.ceil((count || 0) / limit)\n    }\n  })\n})\n\n// POST - Create new product\nexport const POST = withErrorHandler(async (request: NextRequest) => {\n  const productData = await validateRequestBody(request, (body) => {\n    // Validate required fields\n    validateRequiredFields(body, ['name', 'net_weight', 'price', 'category'])\n\n    return {\n      name: String(body.name).trim(),\n      image_url: body.image_url ? String(body.image_url).trim() : null,\n      net_weight: String(body.net_weight).trim(),\n      price: parseFloat(body.price),\n      stock_quantity: parseInt(body.stock_quantity) || 0,\n      category: String(body.category).trim(),\n    }\n  })\n\n  // Additional validation\n  if (productData.price < 0) {\n    return errorResponse('Price must be a positive number', 400)\n  }\n\n  if (productData.stock_quantity < 0) {\n    return errorResponse('Stock quantity must be a positive number', 400)\n  }\n\n  const { data: product, error } = await supabase\n    .from('products')\n    .insert([productData])\n    .select()\n    .single()\n\n  if (error) {\n    handleDatabaseError(error)\n  }\n\n  return successResponse(product, 'Product created successfully', 201)\n})\n"], "names": [], "mappings": ";;;;;AAEA;AAUA;;;AAIO,eAAe;IACpB,OAAO,CAAA,GAAA,4HAAA,CAAA,6BAA0B,AAAD;AAClC;AAGO,MAAM,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;IACzC,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE;IAEtD,mBAAmB;IACnB,MAAM,WAAW,aAAa,GAAG,CAAC;IAClC,MAAM,SAAS,aAAa,GAAG,CAAC;IAChC,MAAM,WAAW,aAAa,GAAG,CAAC,gBAAgB;IAElD,IAAI,QAAQ,wHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,YACL,MAAM,CAAC,KAAK;QAAE,OAAO;IAAQ,GAC7B,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC,QAAQ,SAAS,QAAQ;IAElC,gBAAgB;IAChB,IAAI,UAAU;QACZ,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B;IAEA,IAAI,QAAQ;QACV,QAAQ,MAAM,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC3C;IAEA,IAAI,UAAU;QACZ,QAAQ,MAAM,EAAE,CAAC,kBAAkB;IACrC;IAEA,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;IAE/C,IAAI,OAAO;QACT,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE;IACtB;IAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE;QACrB,UAAU,YAAY,EAAE;QACxB,YAAY;YACV;YACA;YACA,OAAO,SAAS;YAChB,YAAY,KAAK,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI;QACvC;IACF;AACF;AAGO,MAAM,OAAO,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;IAC1C,MAAM,cAAc,MAAM,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,CAAC;QACtD,2BAA2B;QAC3B,CAAA,GAAA,4HAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;YAAC;YAAQ;YAAc;YAAS;SAAW;QAExE,OAAO;YACL,MAAM,OAAO,KAAK,IAAI,EAAE,IAAI;YAC5B,WAAW,KAAK,SAAS,GAAG,OAAO,KAAK,SAAS,EAAE,IAAI,KAAK;YAC5D,YAAY,OAAO,KAAK,UAAU,EAAE,IAAI;YACxC,OAAO,WAAW,KAAK,KAAK;YAC5B,gBAAgB,SAAS,KAAK,cAAc,KAAK;YACjD,UAAU,OAAO,KAAK,QAAQ,EAAE,IAAI;QACtC;IACF;IAEA,wBAAwB;IACxB,IAAI,YAAY,KAAK,GAAG,GAAG;QACzB,OAAO,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD,EAAE,mCAAmC;IAC1D;IAEA,IAAI,YAAY,cAAc,GAAG,GAAG;QAClC,OAAO,CAAA,GAAA,4HAAA,CAAA,gBAAa,AAAD,EAAE,4CAA4C;IACnE;IAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,YACL,MAAM,CAAC;QAAC;KAAY,EACpB,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE;IACtB;IAEA,OAAO,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,gCAAgC;AAClE", "debugId": null}}]}