{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/lib/env.ts"], "sourcesContent": ["// Environment variables validation and configuration\n\nimport { z } from 'zod'\n\n// Define the schema for environment variables\nconst envSchema = z.object({\n  // Node environment\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\n  \n  // Supabase configuration\n  NEXT_PUBLIC_SUPABASE_URL: z.string().optional(),\n  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().optional(),\n  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),\n  \n  // Cloudinary configuration\n  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: z.string().optional(),\n  CLOUDINARY_API_KEY: z.string().optional(),\n  CLOUDINARY_API_SECRET: z.string().optional(),\n\n  // Google Gemini AI configuration\n  GEMINI_API_KEY: z.string().optional(),\n  \n  // Authentication (optional for build time)\n  NEXTAUTH_SECRET: z.string().optional(),\n  NEXTAUTH_URL: z.string().optional(),\n  \n  // Optional configuration\n  DEBUG: z.string().transform(val => val === 'true').default('false'),\n})\n\n// Parse and validate environment variables\nfunction validateEnv() {\n  try {\n    return envSchema.parse(process.env)\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)\n      throw new Error(\n        `❌ Invalid environment variables:\\n${missingVars.join('\\n')}\\n\\n` +\n        `Please check your .env.local file and ensure all required variables are set.\\n` +\n        `See .env.example for reference.`\n      )\n    }\n    throw error\n  }\n}\n\n// Export validated environment variables\nexport const env = validateEnv()\n\n// Environment-specific configurations\nexport const config = {\n  isDevelopment: env.NODE_ENV === 'development',\n  isProduction: env.NODE_ENV === 'production',\n  isTest: env.NODE_ENV === 'test',\n  \n  // Database\n  database: {\n    url: env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',\n    anonKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key',\n    serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,\n  },\n\n  // File storage\n  cloudinary: {\n    cloudName: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'placeholder',\n    apiKey: env.CLOUDINARY_API_KEY,\n    apiSecret: env.CLOUDINARY_API_SECRET,\n  },\n\n  // AI configuration\n  ai: {\n    geminiApiKey: env.GEMINI_API_KEY,\n  },\n  \n  // Authentication\n  auth: {\n    secret: env.NEXTAUTH_SECRET,\n    url: env.NEXTAUTH_URL,\n  },\n  \n  // Debug mode\n  debug: env.DEBUG,\n} as const\n\n// Runtime environment checks\nexport function checkRequiredEnvVars() {\n  const requiredVars = [\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',\n  ]\n  \n  const missingVars = requiredVars.filter(varName => !process.env[varName])\n  \n  if (missingVars.length > 0) {\n    throw new Error(\n      `❌ Missing required environment variables: ${missingVars.join(', ')}\\n` +\n      `Please check your .env.local file.`\n    )\n  }\n}\n\n// Development-only environment checks\nexport function checkDevelopmentEnvVars() {\n  if (config.isDevelopment) {\n    const devVars = [\n      'CLOUDINARY_API_KEY',\n      'CLOUDINARY_API_SECRET',\n      'NEXTAUTH_SECRET',\n    ]\n    \n    const missingDevVars = devVars.filter(varName => !process.env[varName])\n    \n    if (missingDevVars.length > 0) {\n      console.warn(\n        `⚠️  Missing development environment variables: ${missingDevVars.join(', ')}\\n` +\n        `Some features may not work properly.`\n      )\n    }\n  }\n}\n\n// Production-only environment checks\nexport function checkProductionEnvVars() {\n  if (config.isProduction) {\n    const prodVars = [\n      'SUPABASE_SERVICE_ROLE_KEY',\n      'CLOUDINARY_API_KEY',\n      'CLOUDINARY_API_SECRET',\n      'NEXTAUTH_SECRET',\n      'NEXTAUTH_URL',\n    ]\n    \n    const missingProdVars = prodVars.filter(varName => !process.env[varName])\n    \n    if (missingProdVars.length > 0) {\n      throw new Error(\n        `❌ Missing production environment variables: ${missingProdVars.join(', ')}\\n` +\n        `These are required for production deployment.`\n      )\n    }\n  }\n}\n\n// Initialize environment validation\nexport function initializeEnv() {\n  try {\n    checkRequiredEnvVars()\n    checkDevelopmentEnvVars()\n    checkProductionEnvVars()\n    \n    if (config.debug) {\n      console.warn('✅ Environment variables validated successfully')\n      console.warn('📊 Configuration:', {\n        environment: env.NODE_ENV,\n        database: !!config.database.url,\n        cloudinary: !!config.cloudinary.cloudName,\n        auth: !!config.auth.secret,\n      })\n    }\n  } catch (error) {\n    console.error(error)\n    if (config.isProduction) {\n      process.exit(1)\n    }\n  }\n}\n\n// Export individual environment variables for convenience\nexport const {\n  NODE_ENV,\n  NEXT_PUBLIC_SUPABASE_URL,\n  NEXT_PUBLIC_SUPABASE_ANON_KEY,\n  SUPABASE_SERVICE_ROLE_KEY,\n  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,\n  CLOUDINARY_API_KEY,\n  CLOUDINARY_API_SECRET,\n  NEXTAUTH_SECRET,\n  NEXTAUTH_URL,\n  DEBUG,\n  GEMINI_API_KEY,\n} = env\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;;;;;;;;;;;AAErD;;AAEA,8CAA8C;AAC9C,MAAM,YAAY,kKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,mBAAmB;IACnB,UAAU,kKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAEhE,yBAAyB;IACzB,0BAA0B,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7C,+BAA+B,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClD,2BAA2B,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE9C,2BAA2B;IAC3B,mCAAmC,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACtD,oBAAoB,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvC,uBAAuB,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE1C,iCAAiC;IACjC,gBAAgB,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAEnC,2CAA2C;IAC3C,iBAAiB,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,cAAc,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAEjC,yBAAyB;IACzB,OAAO,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAA,MAAO,QAAQ,QAAQ,OAAO,CAAC;AAC7D;AAEA,2CAA2C;AAC3C,SAAS;IACP,IAAI;QACF,OAAO,UAAU,KAAK,CAAC,QAAQ,GAAG;IACpC,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,kKAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,cAAc,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YACnF,MAAM,IAAI,MACR,CAAC,kCAAkC,EAAE,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC,GACjE,CAAC,8EAA8E,CAAC,GAChF,CAAC,+BAA+B,CAAC;QAErC;QACA,MAAM;IACR;AACF;AAGO,MAAM,MAAM;AAGZ,MAAM,SAAS;IACpB,eAAe,IAAI,QAAQ,KAAK;IAChC,cAAc,IAAI,QAAQ,KAAK;IAC/B,QAAQ,IAAI,QAAQ,KAAK;IAEzB,WAAW;IACX,UAAU;QACR,KAAK,IAAI,wBAAwB,IAAI;QACrC,SAAS,IAAI,6BAA6B,IAAI;QAC9C,gBAAgB,IAAI,yBAAyB;IAC/C;IAEA,eAAe;IACf,YAAY;QACV,WAAW,IAAI,iCAAiC,IAAI;QACpD,QAAQ,IAAI,kBAAkB;QAC9B,WAAW,IAAI,qBAAqB;IACtC;IAEA,mBAAmB;IACnB,IAAI;QACF,cAAc,IAAI,cAAc;IAClC;IAEA,iBAAiB;IACjB,MAAM;QACJ,QAAQ,IAAI,eAAe;QAC3B,KAAK,IAAI,YAAY;IACvB;IAEA,aAAa;IACb,OAAO,IAAI,KAAK;AAClB;AAGO,SAAS;IACd,MAAM,eAAe;QACnB;QACA;QACA;KACD;IAED,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;IAExE,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,IAAI,MACR,CAAC,0CAA0C,EAAE,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC,GACvE,CAAC,kCAAkC,CAAC;IAExC;AACF;AAGO,SAAS;IACd,IAAI,OAAO,aAAa,EAAE;QACxB,MAAM,UAAU;YACd;YACA;YACA;SACD;QAED,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;QAEtE,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,QAAQ,IAAI,CACV,CAAC,+CAA+C,EAAE,eAAe,IAAI,CAAC,MAAM,EAAE,CAAC,GAC/E,CAAC,oCAAoC,CAAC;QAE1C;IACF;AACF;AAGO,SAAS;IACd,IAAI,OAAO,YAAY,EAAE;QACvB,MAAM,WAAW;YACf;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;QAExE,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,IAAI,MACR,CAAC,4CAA4C,EAAE,gBAAgB,IAAI,CAAC,MAAM,EAAE,CAAC,GAC7E,CAAC,6CAA6C,CAAC;QAEnD;IACF;AACF;AAGO,SAAS;IACd,IAAI;QACF;QACA;QACA;QAEA,IAAI,OAAO,KAAK,EAAE;YAChB,QAAQ,IAAI,CAAC;YACb,QAAQ,IAAI,CAAC,qBAAqB;gBAChC,aAAa,IAAI,QAAQ;gBACzB,UAAU,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;gBAC/B,YAAY,CAAC,CAAC,OAAO,UAAU,CAAC,SAAS;gBACzC,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM;YAC5B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,IAAI,OAAO,YAAY,EAAE;YACvB,QAAQ,IAAI,CAAC;QACf;IACF;AACF;AAGO,MAAM,EACX,QAAQ,EACR,wBAAwB,EACxB,6BAA6B,EAC7B,yBAAyB,EACzB,iCAAiC,EACjC,kBAAkB,EAClB,qBAAqB,EACrB,eAAe,EACf,YAAY,EACZ,KAAK,EACL,cAAc,EACf,GAAG", "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { config } from './env'\n\n// Create Supabase client with validated environment variables\nexport const supabase = createClient(\n  config.database.url,\n  config.database.anonKey,\n  {\n    auth: {\n      autoRefreshToken: true,\n      persistSession: true,\n      detectSessionInUrl: true\n    },\n    db: {\n      schema: 'public'\n    },\n    global: {\n      headers: {\n        'X-Client-Info': 'revantad-store@1.0.0'\n      }\n    }\n  }\n)\n\n// Database Types\nexport interface Product {\n  id: string\n  name: string\n  image_url?: string\n  net_weight: string\n  price: number\n  stock_quantity: number\n  category: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Customer {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  profile_picture_url?: string\n  profile_picture_public_id?: string\n  phone_number?: string\n  address?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerDebt {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  product_name: string\n  product_price: number\n  quantity: number\n  total_amount: number\n  debt_date: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerPayment {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  payment_amount: number\n  payment_date: string\n  payment_method: string\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerBalance {\n  customer_name: string\n  customer_family_name: string\n  total_debt: number\n  total_payments: number\n  remaining_balance: number\n  last_debt_date?: string\n  last_payment_date?: string\n}\n\n// Product Categories\nexport const PRODUCT_CATEGORIES = [\n  'Snacks',\n  'Canned Goods',\n  'Beverages',\n  'Personal Care',\n  'Household Items',\n  'Condiments',\n  'Rice & Grains',\n  'Instant Foods',\n  'Dairy Products',\n  'Others'\n] as const\n\nexport type ProductCategory = typeof PRODUCT_CATEGORIES[number]\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACjC,iHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,GAAG,EACnB,iHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,OAAO,EACvB;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,IAAI;QACF,QAAQ;IACV;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;QACnB;IACF;AACF;AAiEK,MAAM,qBAAqB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/utils/exportUtils.ts"], "sourcesContent": ["import { Product, CustomerDebt } from '@/lib/supabase'\n\n// Export products to CSV\nexport function exportProductsToCSV(products: Product[], filename = 'products') {\n  const headers = [\n    'ID',\n    'Name',\n    'Category',\n    'Price',\n    'Stock Quantity',\n    'Net Weight',\n    'Image URL',\n    'Created At',\n    'Updated At'\n  ]\n\n  const csvContent = [\n    headers.join(','),\n    ...products.map(product => [\n      product.id,\n      `\"${product.name.replace(/\"/g, '\"\"')}\"`, // Escape quotes\n      `\"${product.category}\"`,\n      product.price,\n      product.stock_quantity,\n      `\"${product.net_weight}\"`,\n      product.image_url || '',\n      product.created_at,\n      product.updated_at\n    ].join(','))\n  ].join('\\n')\n\n  downloadFile(csvContent, `${filename}.csv`, 'text/csv')\n}\n\n// Export products to JSON\nexport function exportProductsToJSON(products: Product[], filename = 'products') {\n  const jsonContent = JSON.stringify(products, null, 2)\n  downloadFile(jsonContent, `${filename}.json`, 'application/json')\n}\n\n// Export debts to CSV\nexport function exportDebtsToCSV(debts: CustomerDebt[], filename = 'customer_debts') {\n  const headers = [\n    'ID',\n    'Customer Name',\n    'Amount',\n    'Description',\n    'Status',\n    'Due Date',\n    'Created At',\n    'Updated At'\n  ]\n\n  const csvContent = [\n    headers.join(','),\n    ...debts.map(debt => [\n      debt.id,\n      `\"${debt.customer_name.replace(/\"/g, '\"\"')}\"`,\n      debt.amount,\n      `\"${debt.description?.replace(/\"/g, '\"\"') || ''}\"`,\n      debt.status,\n      debt.due_date || '',\n      debt.created_at,\n      debt.updated_at\n    ].join(','))\n  ].join('\\n')\n\n  downloadFile(csvContent, `${filename}.csv`, 'text/csv')\n}\n\n// Generic download function\nfunction downloadFile(content: string, filename: string, mimeType: string) {\n  const blob = new Blob([content], { type: mimeType })\n  const url = URL.createObjectURL(blob)\n  \n  const link = document.createElement('a')\n  link.href = url\n  link.download = filename\n  link.style.display = 'none'\n  \n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  \n  URL.revokeObjectURL(url)\n}\n\n// Generate product analytics report\nexport function generateProductAnalyticsReport(products: Product[]) {\n  const totalProducts = products.length\n  const totalValue = products.reduce((sum, product) => sum + (product.price * product.stock_quantity), 0)\n  const lowStockItems = products.filter(product => product.stock_quantity < 10).length\n  const outOfStockItems = products.filter(product => product.stock_quantity === 0).length\n  \n  const categoryStats = products.reduce((stats, product) => {\n    if (!stats[product.category]) {\n      stats[product.category] = {\n        count: 0,\n        totalValue: 0,\n        averagePrice: 0,\n        totalStock: 0\n      }\n    }\n    \n    stats[product.category].count++\n    stats[product.category].totalValue += product.price * product.stock_quantity\n    stats[product.category].totalStock += product.stock_quantity\n    \n    return stats\n  }, {} as Record<string, any>)\n\n  // Calculate average prices\n  Object.keys(categoryStats).forEach(category => {\n    const categoryProducts = products.filter(p => p.category === category)\n    categoryStats[category].averagePrice = \n      categoryProducts.reduce((sum, p) => sum + p.price, 0) / categoryProducts.length\n  })\n\n  const report = {\n    summary: {\n      totalProducts,\n      totalValue: totalValue.toFixed(2),\n      lowStockItems,\n      outOfStockItems,\n      averageProductValue: (totalValue / totalProducts).toFixed(2)\n    },\n    categoryBreakdown: categoryStats,\n    generatedAt: new Date().toISOString()\n  }\n\n  return report\n}\n\n// Export analytics report\nexport function exportAnalyticsReport(products: Product[], filename = 'product_analytics') {\n  const report = generateProductAnalyticsReport(products)\n  const jsonContent = JSON.stringify(report, null, 2)\n  downloadFile(jsonContent, `${filename}.json`, 'application/json')\n}\n\n// Format currency for display\nexport function formatCurrency(amount: number, currency = '₱') {\n  return `${currency}${amount.toFixed(2)}`\n}\n\n// Format date for display\nexport function formatDate(dateString: string) {\n  return new Date(dateString).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n\n// Generate stock status\nexport function getStockStatus(quantity: number) {\n  if (quantity === 0) return { status: 'out-of-stock', label: 'Out of Stock', color: 'red' }\n  if (quantity < 10) return { status: 'low-stock', label: 'Low Stock', color: 'orange' }\n  return { status: 'in-stock', label: 'In Stock', color: 'green' }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAGO,SAAS,oBAAoB,QAAmB,EAAE,WAAW,UAAU;IAC5E,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa;QACjB,QAAQ,IAAI,CAAC;WACV,SAAS,GAAG,CAAC,CAAA,UAAW;gBACzB,QAAQ,EAAE;gBACV,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;gBACvC,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC,CAAC,CAAC;gBACvB,QAAQ,KAAK;gBACb,QAAQ,cAAc;gBACtB,CAAC,CAAC,EAAE,QAAQ,UAAU,CAAC,CAAC,CAAC;gBACzB,QAAQ,SAAS,IAAI;gBACrB,QAAQ,UAAU;gBAClB,QAAQ,UAAU;aACnB,CAAC,IAAI,CAAC;KACR,CAAC,IAAI,CAAC;IAEP,aAAa,YAAY,GAAG,SAAS,IAAI,CAAC,EAAE;AAC9C;AAGO,SAAS,qBAAqB,QAAmB,EAAE,WAAW,UAAU;IAC7E,MAAM,cAAc,KAAK,SAAS,CAAC,UAAU,MAAM;IACnD,aAAa,aAAa,GAAG,SAAS,KAAK,CAAC,EAAE;AAChD;AAGO,SAAS,iBAAiB,KAAqB,EAAE,WAAW,gBAAgB;IACjF,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa;QACjB,QAAQ,IAAI,CAAC;WACV,MAAM,GAAG,CAAC,CAAA,OAAQ;gBACnB,KAAK,EAAE;gBACP,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;gBAC7C,KAAK,MAAM;gBACX,CAAC,CAAC,EAAE,KAAK,WAAW,EAAE,QAAQ,MAAM,SAAS,GAAG,CAAC,CAAC;gBAClD,KAAK,MAAM;gBACX,KAAK,QAAQ,IAAI;gBACjB,KAAK,UAAU;gBACf,KAAK,UAAU;aAChB,CAAC,IAAI,CAAC;KACR,CAAC,IAAI,CAAC;IAEP,aAAa,YAAY,GAAG,SAAS,IAAI,CAAC,EAAE;AAC9C;AAEA,4BAA4B;AAC5B,SAAS,aAAa,OAAe,EAAE,QAAgB,EAAE,QAAgB;IACvE,MAAM,OAAO,IAAI,KAAK;QAAC;KAAQ,EAAE;QAAE,MAAM;IAAS;IAClD,MAAM,MAAM,IAAI,eAAe,CAAC;IAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,KAAK,KAAK,CAAC,OAAO,GAAG;IAErB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAE1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,+BAA+B,QAAmB;IAChE,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAO,QAAQ,KAAK,GAAG,QAAQ,cAAc,EAAG;IACrG,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,cAAc,GAAG,IAAI,MAAM;IACpF,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,cAAc,KAAK,GAAG,MAAM;IAEvF,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,OAAO;QAC5C,IAAI,CAAC,KAAK,CAAC,QAAQ,QAAQ,CAAC,EAAE;YAC5B,KAAK,CAAC,QAAQ,QAAQ,CAAC,GAAG;gBACxB,OAAO;gBACP,YAAY;gBACZ,cAAc;gBACd,YAAY;YACd;QACF;QAEA,KAAK,CAAC,QAAQ,QAAQ,CAAC,CAAC,KAAK;QAC7B,KAAK,CAAC,QAAQ,QAAQ,CAAC,CAAC,UAAU,IAAI,QAAQ,KAAK,GAAG,QAAQ,cAAc;QAC5E,KAAK,CAAC,QAAQ,QAAQ,CAAC,CAAC,UAAU,IAAI,QAAQ,cAAc;QAE5D,OAAO;IACT,GAAG,CAAC;IAEJ,2BAA2B;IAC3B,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,CAAA;QACjC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QAC7D,aAAa,CAAC,SAAS,CAAC,YAAY,GAClC,iBAAiB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE,KAAK,iBAAiB,MAAM;IACnF;IAEA,MAAM,SAAS;QACb,SAAS;YACP;YACA,YAAY,WAAW,OAAO,CAAC;YAC/B;YACA;YACA,qBAAqB,CAAC,aAAa,aAAa,EAAE,OAAO,CAAC;QAC5D;QACA,mBAAmB;QACnB,aAAa,IAAI,OAAO,WAAW;IACrC;IAEA,OAAO;AACT;AAGO,SAAS,sBAAsB,QAAmB,EAAE,WAAW,mBAAmB;IACvF,MAAM,SAAS,+BAA+B;IAC9C,MAAM,cAAc,KAAK,SAAS,CAAC,QAAQ,MAAM;IACjD,aAAa,aAAa,GAAG,SAAS,KAAK,CAAC,EAAE;AAChD;AAGO,SAAS,eAAe,MAAc,EAAE,WAAW,GAAG;IAC3D,OAAO,GAAG,WAAW,OAAO,OAAO,CAAC,IAAI;AAC1C;AAGO,SAAS,WAAW,UAAkB;IAC3C,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;QACtD,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAGO,SAAS,eAAe,QAAgB;IAC7C,IAAI,aAAa,GAAG,OAAO;QAAE,QAAQ;QAAgB,OAAO;QAAgB,OAAO;IAAM;IACzF,IAAI,WAAW,IAAI,OAAO;QAAE,QAAQ;QAAa,OAAO;QAAa,OAAO;IAAS;IACrF,OAAO;QAAE,QAAQ;QAAY,OAAO;QAAY,OAAO;IAAQ;AACjE", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n\nimport {\n  AdminHeader,\n  Sidebar,\n  ProductsSection,\n  DebtsSection,\n  DashboardStats,\n  FamilyGallery,\n  APIGraphing,\n  History,\n  Calendar,\n  Settings,\n  ProtectedRoute,\n  AIAssistant,\n  AISupport\n} from '@/components'\nimport type { DashboardStats as DashboardStatsType } from '@/types'\n\nexport default function AdminPage() {\n  const [activeSection, setActiveSection] = useState('dashboard')\n  const { resolvedTheme } = useTheme()\n  const [stats, setStats] = useState<DashboardStatsType>({\n    totalProducts: 0,\n    totalDebts: 0,\n    totalDebtAmount: 0,\n    lowStockItems: 0,\n    recentProducts: [],\n    recentDebts: []\n  })\n\n  useEffect(() => {\n    fetchStats()\n  }, [])\n\n  const fetchStats = async () => {\n    try {\n      // Fetch products\n      const productsRes = await fetch('/api/products')\n      const productsData = await productsRes.json()\n      const products = productsData.products || []\n\n      // Fetch debts\n      const debtsRes = await fetch('/api/debts')\n      const debtsData = await debtsRes.json()\n      const debts = debtsData.debts || []\n\n      // Calculate stats\n      const totalDebtAmount = debts.reduce((sum: number, debt: { total_amount: number }) => sum + debt.total_amount, 0)\n      const lowStockProducts = products.filter((product: { stock_quantity: number }) => product.stock_quantity < 10).length\n\n      setStats({\n        totalProducts: products.length,\n        totalDebts: debts.length,\n        totalDebtAmount,\n        lowStockItems: lowStockProducts,\n        recentProducts: products.slice(0, 5),\n        recentDebts: debts.slice(0, 5)\n      })\n    } catch (error) {\n      console.error('Error fetching stats:', error)\n    }\n  }\n\n  const renderContent = () => {\n    console.warn('🎯 Rendering content for section:', activeSection)\n\n    switch (activeSection) {\n      case 'products':\n        console.warn('📦 Rendering ProductsSection')\n        return <ProductsSection onStatsUpdate={fetchStats} />\n      case 'debts':\n        console.warn('💳 Rendering DebtsSection')\n        return <DebtsSection onStatsUpdate={fetchStats} />\n      case 'family-gallery':\n        console.warn('🖼️ Rendering FamilyGallery')\n        return <FamilyGallery />\n      case 'api-graphing':\n        console.warn('📊 Rendering APIGraphing')\n        return <APIGraphing stats={stats} />\n      case 'history':\n        console.warn('📜 Rendering History')\n        return <History />\n      case 'calendar':\n        console.warn('📅 Rendering Calendar')\n        return <Calendar />\n      case 'settings':\n        console.warn('⚙️ Rendering Settings')\n        return <Settings />\n      case 'ai-support':\n        console.warn('🤖 Rendering AISupport')\n        return <AISupport />\n      default:\n        console.warn('🏠 Rendering DashboardStats (default)')\n        return <DashboardStats stats={stats} onSectionChange={setActiveSection} />\n    }\n  }\n\n  const getPageTitle = () => {\n    switch (activeSection) {\n      case 'dashboard':\n        return 'Dashboard'\n      case 'products':\n        return 'Product Lists'\n      case 'debts':\n        return 'Customer Debt Management'\n      case 'family-gallery':\n        return 'Family Gallery'\n      case 'api-graphing':\n        return 'API Graphing & Visuals'\n      case 'history':\n        return 'History'\n      case 'calendar':\n        return 'Calendar'\n      case 'settings':\n        return 'Settings'\n      case 'ai-support':\n        return 'AI Support'\n      default:\n        return 'Dashboard'\n    }\n  }\n\n  const getPageDescription = () => {\n    switch (activeSection) {\n      case 'dashboard':\n        return 'Overview of your Revantad Store'\n      case 'products':\n        return 'Manage your product lists with CRUD operations'\n      case 'debts':\n        return 'Track customer debt and payments'\n      case 'family-gallery':\n        return 'Manage family photos and memories'\n      case 'api-graphing':\n        return 'Visual analytics and business insights'\n      case 'history':\n        return 'View transaction and activity history'\n      case 'calendar':\n        return 'Manage events and schedules'\n      case 'settings':\n        return 'Configure your store settings'\n      case 'ai-support':\n        return 'Get intelligent assistance for your store management'\n      default:\n        return 'Overview of your Revantad Store'\n    }\n  }\n\n  return (\n    <ProtectedRoute>\n      <div\n        className=\"min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300\"\n        style={{\n          backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#f9fafb'\n        }}\n      >\n        {/* Facebook-style Header */}\n        <AdminHeader\n          activeSection={activeSection}\n          setActiveSection={setActiveSection}\n        />\n\n        <div className=\"flex pt-16\">\n          {/* Updated Sidebar */}\n          <Sidebar activeSection={activeSection} setActiveSection={setActiveSection} />\n\n          {/* Main Content */}\n          <main\n            className=\"flex-1 transition-colors duration-300 main-content-scroll\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#ffffff',\n              height: 'calc(100vh - 4rem)',\n              overflowY: 'auto',\n              overflowX: 'hidden'\n            }}\n          >\n            <div className=\"p-8\">\n              <div className=\"mb-8\">\n                <h1\n                  className=\"text-3xl font-bold transition-colors duration-300\"\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937'\n                  }}\n                >\n                  {getPageTitle()}\n                </h1>\n                <p\n                  className=\"mt-2 transition-colors duration-300\"\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                  }}\n                >\n                  {getPageDescription()}\n                </p>\n              </div>\n              {renderContent()}\n            </div>\n          </main>\n        </div>\n\n        {/* AI Assistant */}\n        <AIAssistant context={activeSection} />\n      </div>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAsBe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,eAAe;QACf,YAAY;QACZ,iBAAiB;QACjB,eAAe;QACf,gBAAgB,EAAE;QAClB,aAAa,EAAE;IACjB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,iBAAiB;YACjB,MAAM,cAAc,MAAM,MAAM;YAChC,MAAM,eAAe,MAAM,YAAY,IAAI;YAC3C,MAAM,WAAW,aAAa,QAAQ,IAAI,EAAE;YAE5C,cAAc;YACd,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,QAAQ,UAAU,KAAK,IAAI,EAAE;YAEnC,kBAAkB;YAClB,MAAM,kBAAkB,MAAM,MAAM,CAAC,CAAC,KAAa,OAAmC,MAAM,KAAK,YAAY,EAAE;YAC/G,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,UAAwC,QAAQ,cAAc,GAAG,IAAI,MAAM;YAErH,SAAS;gBACP,eAAe,SAAS,MAAM;gBAC9B,YAAY,MAAM,MAAM;gBACxB;gBACA,eAAe;gBACf,gBAAgB,SAAS,KAAK,CAAC,GAAG;gBAClC,aAAa,MAAM,KAAK,CAAC,GAAG;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,gBAAgB;QACpB,QAAQ,IAAI,CAAC,qCAAqC;QAElD,OAAQ;YACN,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,qBAAO,8OAAC,mLAAA,CAAA,kBAAe;oBAAC,eAAe;;;;;;YACzC,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,qBAAO,8OAAC,6KAAA,CAAA,eAAY;oBAAC,eAAe;;;;;;YACtC,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,qBAAO,8OAAC,+KAAA,CAAA,gBAAa;;;;;YACvB,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,qBAAO,8OAAC,2KAAA,CAAA,cAAW;oBAAC,OAAO;;;;;;YAC7B,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,qBAAO,8OAAC,mKAAA,CAAA,UAAO;;;;;YACjB,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,qBAAO,8OAAC,qKAAA,CAAA,WAAQ;;;;;YAClB,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,qBAAO,8OAAC,qKAAA,CAAA,WAAQ;;;;;YAClB,KAAK;gBACH,QAAQ,IAAI,CAAC;gBACb,qBAAO,8OAAC,uKAAA,CAAA,YAAS;;;;;YACnB;gBACE,QAAQ,IAAI,CAAC;gBACb,qBAAO,8OAAC,iLAAA,CAAA,iBAAc;oBAAC,OAAO;oBAAO,iBAAiB;;;;;;QAC1D;IACF;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,iLAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC;YACC,WAAU;YACV,OAAO;gBACL,iBAAiB,kBAAkB,SAAS,YAAY;YAC1D;;8BAGA,8OAAC,2KAAA,CAAA,cAAW;oBACV,eAAe;oBACf,kBAAkB;;;;;;8BAGpB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,mKAAA,CAAA,UAAO;4BAAC,eAAe;4BAAe,kBAAkB;;;;;;sCAGzD,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAAS,YAAY;gCACxD,QAAQ;gCACR,WAAW;gCACX,WAAW;4BACb;sCAEA,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DAEC;;;;;;0DAEH,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DAEC;;;;;;;;;;;;oCAGJ;;;;;;;;;;;;;;;;;;8BAMP,8OAAC,2KAAA,CAAA,cAAW;oBAAC,SAAS;;;;;;;;;;;;;;;;;AAI9B", "debugId": null}}]}