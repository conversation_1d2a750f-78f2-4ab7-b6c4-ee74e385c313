{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/components/DebtsSection.tsx"], "sourcesContent": ["'use client'\n\nimport { format, differenceInDays } from 'date-fns'\nimport {\n  Plus, Edit, Trash2, Search, Users, Calendar, Filter, SortAsc, SortDesc,\n  Grid, List, Download, CheckSquare, Square, Minus, ChevronDown, BarChart3,\n  RefreshCw, Clock, DollarSign, FileText, Calculator, Package, AlertTriangle\n} from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect, useMemo, useCallback } from 'react'\n\nimport { CustomerDebt, CustomerBalance, CustomerPayment, Customer } from '@/lib/supabase'\nimport { exportDebtsToCSV } from '@/utils/exportUtils'\n\nimport DebtModal from './DebtModal'\nimport PaymentModal from './PaymentModal'\nimport CustomerProfileModal from './CustomerProfileModal'\nimport CustomerAvatar, { useCustomerProfile } from './CustomerAvatar'\nimport LoadingSkeleton from './LoadingSkeleton'\n\ninterface DebtsSectionProps {\n  onStatsUpdate: () => void\n}\n\n// Enhanced filter and sort types for debts\ntype DebtSortOption = 'customer_name' | 'total_amount' | 'debt_date' | 'created_at' | 'product_name'\ntype SortDirection = 'asc' | 'desc'\ntype ViewMode = 'grid' | 'list' | 'customer'\ntype DebtStatus = 'all' | 'recent' | 'overdue' | 'high-amount'\n\ninterface DebtFilterOptions {\n  search: string\n  dateRange: { from: string; to: string }\n  amountRange: { min: number; max: number }\n  status: DebtStatus\n  customer: string\n}\n\ninterface CustomerSummary {\n  customerName: string\n  totalDebts: number\n  totalAmount: number\n  totalPayments: number\n  remainingBalance: number\n  oldestDebt: string\n  recentDebt: string\n  debts: CustomerDebt[]\n}\n\nexport default function DebtsSection({ onStatsUpdate }: DebtsSectionProps) {\n  const { resolvedTheme } = useTheme()\n  const [debts, setDebts] = useState<CustomerDebt[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [isModalOpen, setIsModalOpen] = useState(false)\n  const [editingDebt, setEditingDebt] = useState<CustomerDebt | null>(null)\n\n  // Enhanced features state\n  const [viewMode, setViewMode] = useState<ViewMode>('customer')\n  const [sortBy, setSortBy] = useState<DebtSortOption>('debt_date')\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')\n  const [selectedDebts, setSelectedDebts] = useState<Set<string>>(new Set())\n  const [showFilters, setShowFilters] = useState(false)\n  const [filters, setFilters] = useState<DebtFilterOptions>({\n    search: '',\n    dateRange: { from: '', to: '' },\n    amountRange: { min: 0, max: 50000 },\n    status: 'all',\n    customer: ''\n  })\n  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([])\n  const [showSuggestions, setShowSuggestions] = useState(false)\n\n  // Payment functionality\n  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)\n  const [selectedCustomerForPayment, setSelectedCustomerForPayment] = useState<{\n    name: string\n    familyName: string\n    balance: number\n  } | null>(null)\n  const [customerBalances, setCustomerBalances] = useState<Map<string, CustomerBalance>>(new Map())\n  const [editingPayment, setEditingPayment] = useState<CustomerPayment | null>(null)\n\n  // Customer profile functionality\n  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false)\n  const [selectedCustomerForProfile, setSelectedCustomerForProfile] = useState<{\n    name: string\n    familyName: string\n    customer?: Customer\n  } | null>(null)\n  const [avatarRefreshKey, setAvatarRefreshKey] = useState(0)\n  const [customerProfiles, setCustomerProfiles] = useState<Map<string, Customer>>(new Map())\n  const { getCustomerProfile, updateCustomerProfile } = useCustomerProfile()\n\n  useEffect(() => {\n    fetchDebts()\n    fetchCustomerBalances()\n    fetchCustomerProfiles()\n  }, [])\n\n  const fetchCustomerBalances = async () => {\n    try {\n      const response = await fetch('/api/customer-balances')\n      if (response.ok) {\n        const data = await response.json()\n        const balanceMap = new Map<string, CustomerBalance>()\n        // Check if balances array exists and is an array\n        if (data.balances && Array.isArray(data.balances)) {\n          data.balances.forEach((balance: CustomerBalance) => {\n            const key = `${balance.customer_name} ${balance.customer_family_name}`\n            balanceMap.set(key, balance)\n          })\n        }\n        setCustomerBalances(balanceMap)\n      }\n    } catch (error) {\n      console.error('Error fetching customer balances:', error)\n      // Set empty map on error to prevent crashes\n      setCustomerBalances(new Map())\n    }\n  }\n\n  const fetchCustomerProfiles = async () => {\n    try {\n      const response = await fetch('/api/customers')\n      if (response.ok) {\n        const data = await response.json()\n        const profileMap = new Map<string, Customer>()\n        // Check if customers array exists and is an array\n        if (data.customers && Array.isArray(data.customers)) {\n          data.customers.forEach((customer: Customer) => {\n            const key = `${customer.customer_name} ${customer.customer_family_name}`\n            profileMap.set(key, customer)\n          })\n        }\n        setCustomerProfiles(profileMap)\n      }\n    } catch (error) {\n      console.error('Error fetching customer profiles:', error)\n      // Set empty map on error to prevent crashes\n      setCustomerProfiles(new Map())\n    }\n  }\n\n  const fetchDebts = async () => {\n    try {\n      // Using console.warn for informational messages (ESLint compliant)\n      console.warn('🔄 Fetching debts...')\n      const response = await fetch('/api/debts')\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n\n      const data = await response.json()\n      console.warn('💳 Debts API response:', data)\n\n      // Handle new API structure: { success: true, data: { debts: [...] } }\n      if (data.success && data.data && data.data.debts) {\n        setDebts(data.data.debts)\n        console.warn('✅ Debts loaded (new structure):', data.data.debts.length, 'items')\n      }\n      // Handle old API structure: { debts: [...] }\n      else if (data.debts) {\n        setDebts(data.debts)\n        console.warn('✅ Debts loaded (old structure):', data.debts.length, 'items')\n      }\n      // Handle direct array\n      else if (Array.isArray(data)) {\n        setDebts(data)\n        console.warn('✅ Debts loaded (direct array):', data.length, 'items')\n      }\n      else {\n        console.warn('⚠️ Unexpected API response structure:', data)\n        setDebts([])\n      }\n    } catch (error) {\n      console.error('❌ Error fetching debts:', error)\n      setDebts([])\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDelete = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this debt record?')) return\n\n    try {\n      const response = await fetch(`/api/debts/${id}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        setDebts(debts.filter(d => d.id !== id))\n        onStatsUpdate()\n      }\n    } catch (error) {\n      console.error('Error deleting debt:', error)\n    }\n  }\n\n  const handleEdit = (debt: CustomerDebt) => {\n    setEditingDebt(debt)\n    setIsModalOpen(true)\n  }\n\n  const handleModalClose = () => {\n    setIsModalOpen(false)\n    setEditingDebt(null)\n    fetchDebts()\n    fetchCustomerBalances()\n    onStatsUpdate()\n  }\n\n  const handlePaymentModalClose = () => {\n    setIsPaymentModalOpen(false)\n    setSelectedCustomerForPayment(null)\n    setEditingPayment(null)\n    fetchCustomerBalances()\n    onStatsUpdate()\n  }\n\n  const handleMakePayment = (customerName: string, customerFamilyName: string) => {\n    const customerKey = `${customerName} ${customerFamilyName}`\n    const balance = customerBalances.get(customerKey)\n\n    setSelectedCustomerForPayment({\n      name: customerName,\n      familyName: customerFamilyName,\n      balance: balance?.remaining_balance || 0\n    })\n    setIsPaymentModalOpen(true)\n  }\n\n  const handleProfileModalClose = () => {\n    setIsProfileModalOpen(false)\n    setSelectedCustomerForProfile(null)\n    // Refresh customer data to show updated profiles\n    fetchCustomerBalances()\n    fetchCustomerProfiles()\n  }\n\n  const handleProfileUpdated = (updatedCustomer: Customer) => {\n    // Update the customer profile in the local state\n    updateCustomerProfile(updatedCustomer.customer_name, updatedCustomer.customer_family_name, updatedCustomer)\n\n    // Update local profiles map\n    const key = `${updatedCustomer.customer_name} ${updatedCustomer.customer_family_name}`\n    setCustomerProfiles(prev => new Map(prev.set(key, updatedCustomer)))\n\n    // Force avatar refresh by incrementing the key\n    setAvatarRefreshKey(prev => prev + 1)\n\n    // Refresh the entire customer data\n    fetchCustomerBalances()\n    fetchCustomerProfiles()\n  }\n\n  const handleEditProfile = async (customerName: string, customerFamilyName: string) => {\n    const customer = await getCustomerProfile(customerName, customerFamilyName)\n    setSelectedCustomerForProfile({\n      name: customerName,\n      familyName: customerFamilyName,\n      customer\n    })\n    setIsProfileModalOpen(true)\n  }\n\n  // Advanced search suggestions\n  const generateSearchSuggestions = useCallback((term: string) => {\n    if (!term || term.length < 2) {\n      setSearchSuggestions([])\n      return\n    }\n\n    const suggestions = debts\n      .filter(debt => {\n        const customerName = `${debt.customer_name} ${debt.customer_family_name}`.toLowerCase()\n        const productName = debt.product_name.toLowerCase()\n        return customerName.includes(term.toLowerCase()) || productName.includes(term.toLowerCase())\n      })\n      .map(debt => `${debt.customer_name} ${debt.customer_family_name}`)\n      .slice(0, 5)\n\n    setSearchSuggestions([...new Set(suggestions)])\n  }, [debts])\n\n  // Advanced filtering and sorting logic\n  const filteredAndSortedDebts = useMemo(() => {\n    const filtered = debts.filter(debt => {\n      // Text search\n      const customerName = `${debt.customer_name} ${debt.customer_family_name}`.toLowerCase()\n      const productName = debt.product_name.toLowerCase()\n      const searchText = (filters.search || searchTerm).toLowerCase()\n      const matchesSearch = !searchText ||\n        customerName.includes(searchText) ||\n        productName.includes(searchText)\n\n      // Date range filter\n      const debtDate = new Date(debt.debt_date)\n      const matchesDateRange = (!filters.dateRange.from || debtDate >= new Date(filters.dateRange.from)) &&\n        (!filters.dateRange.to || debtDate <= new Date(filters.dateRange.to))\n\n      // Amount range filter\n      const matchesAmount = debt.total_amount >= filters.amountRange.min &&\n        debt.total_amount <= filters.amountRange.max\n\n      // Status filter\n      let matchesStatus = true\n      if (filters.status !== 'all') {\n        const daysDiff = differenceInDays(new Date(), debtDate)\n        switch (filters.status) {\n          case 'recent':\n            matchesStatus = daysDiff <= 7\n            break\n          case 'overdue':\n            matchesStatus = daysDiff > 30\n            break\n          case 'high-amount':\n            matchesStatus = debt.total_amount > 1000\n            break\n        }\n      }\n\n      // Customer filter\n      const matchesCustomer = !filters.customer ||\n        customerName.includes(filters.customer.toLowerCase())\n\n      return matchesSearch && matchesDateRange && matchesAmount && matchesStatus && matchesCustomer\n    })\n\n    // Sorting\n    filtered.sort((a, b) => {\n      let aValue: string | number, bValue: string | number\n\n      switch (sortBy) {\n        case 'customer_name':\n          aValue = `${a.customer_name} ${a.customer_family_name}`.toLowerCase()\n          bValue = `${b.customer_name} ${b.customer_family_name}`.toLowerCase()\n          break\n        case 'total_amount':\n          aValue = a.total_amount\n          bValue = b.total_amount\n          break\n        case 'debt_date':\n          aValue = new Date(a.debt_date).getTime()\n          bValue = new Date(b.debt_date).getTime()\n          break\n        case 'product_name':\n          aValue = a.product_name.toLowerCase()\n          bValue = b.product_name.toLowerCase()\n          break\n        case 'created_at':\n          aValue = new Date(a.created_at).getTime()\n          bValue = new Date(b.created_at).getTime()\n          break\n        default:\n          return 0\n      }\n\n      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1\n      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1\n      return 0\n    })\n\n    return filtered\n  }, [debts, searchTerm, filters, sortBy, sortDirection])\n\n  // Customer summary for customer view mode\n  const customerSummaries = useMemo(() => {\n    const summaries: CustomerSummary[] = []\n    const customerMap = new Map<string, CustomerDebt[]>()\n\n    filteredAndSortedDebts.forEach(debt => {\n      const customerKey = `${debt.customer_name} ${debt.customer_family_name}`\n      if (!customerMap.has(customerKey)) {\n        customerMap.set(customerKey, [])\n      }\n      customerMap.get(customerKey)!.push(debt)\n    })\n\n    customerMap.forEach((debts, customerName) => {\n      const totalAmount = debts.reduce((sum, debt) => sum + debt.total_amount, 0)\n      const sortedDebts = debts.sort((a, b) => new Date(a.debt_date).getTime() - new Date(b.debt_date).getTime())\n\n      // Get balance information\n      const balance = customerBalances.get(customerName)\n      const totalPayments = balance?.total_payments || 0\n      const remainingBalance = balance?.remaining_balance || totalAmount\n\n      summaries.push({\n        customerName,\n        totalDebts: debts.length,\n        totalAmount,\n        totalPayments,\n        remainingBalance,\n        oldestDebt: sortedDebts[0]?.debt_date || '',\n        recentDebt: sortedDebts[sortedDebts.length - 1]?.debt_date || '',\n        debts: sortedDebts\n      })\n    })\n\n    return summaries.sort((a, b) => b.totalAmount - a.totalAmount)\n  }, [filteredAndSortedDebts, customerBalances])\n\n  // Bulk operations handlers\n  const handleSelectAll = () => {\n    if (selectedDebts.size === filteredAndSortedDebts.length) {\n      setSelectedDebts(new Set())\n    } else {\n      setSelectedDebts(new Set(filteredAndSortedDebts.map(d => d.id)))\n    }\n  }\n\n  const handleSelectDebt = (debtId: string) => {\n    const newSelected = new Set(selectedDebts)\n    if (newSelected.has(debtId)) {\n      newSelected.delete(debtId)\n    } else {\n      newSelected.add(debtId)\n    }\n    setSelectedDebts(newSelected)\n  }\n\n  const handleBulkDelete = async () => {\n    if (selectedDebts.size === 0) return\n\n    const confirmMessage = `Are you sure you want to delete ${selectedDebts.size} debt record(s)?`\n    if (!confirm(confirmMessage)) return\n\n    try {\n      const deletePromises = Array.from(selectedDebts).map(id =>\n        fetch(`/api/debts/${id}`, { method: 'DELETE' })\n      )\n\n      await Promise.all(deletePromises)\n      setSelectedDebts(new Set())\n      fetchDebts()\n      onStatsUpdate()\n    } catch (error) {\n      console.error('Error deleting debt records:', error)\n    }\n  }\n\n  // Search handlers\n  const handleSearchChange = (value: string) => {\n    setSearchTerm(value)\n    setFilters(prev => ({ ...prev, search: value }))\n    generateSearchSuggestions(value)\n    setShowSuggestions(value.length >= 2)\n  }\n\n  const handleSuggestionClick = (suggestion: string) => {\n    setSearchTerm(suggestion)\n    setFilters(prev => ({ ...prev, search: suggestion }))\n    setShowSuggestions(false)\n  }\n\n\n\n  // Export handlers\n  const handleExportCSV = () => {\n    exportDebtsToCSV(filteredAndSortedDebts, `customer_debts_${new Date().toISOString().split('T')[0]}`)\n  }\n\n  const handleExportJSON = () => {\n    const jsonContent = JSON.stringify(filteredAndSortedDebts, null, 2)\n    const blob = new Blob([jsonContent], { type: 'application/json' })\n    const url = URL.createObjectURL(blob)\n    const link = document.createElement('a')\n    link.href = url\n    link.download = `customer_debts_${new Date().toISOString().split('T')[0]}.json`\n    link.click()\n    URL.revokeObjectURL(url)\n  }\n\n  const handleExportAnalytics = () => {\n    const report = {\n      summary: {\n        totalDebts: filteredAndSortedDebts.length,\n        totalAmount: filteredAndSortedDebts.reduce((sum, debt) => sum + debt.total_amount, 0),\n        uniqueCustomers: new Set(filteredAndSortedDebts.map(d => `${d.customer_name} ${d.customer_family_name}`)).size,\n        averageDebtAmount: filteredAndSortedDebts.length > 0\n          ? filteredAndSortedDebts.reduce((sum, debt) => sum + debt.total_amount, 0) / filteredAndSortedDebts.length\n          : 0\n      },\n      customerBreakdown: customerSummaries,\n      generatedAt: new Date().toISOString()\n    }\n\n    const jsonContent = JSON.stringify(report, null, 2)\n    const blob = new Blob([jsonContent], { type: 'application/json' })\n    const url = URL.createObjectURL(blob)\n    const link = document.createElement('a')\n    link.href = url\n    link.download = `debt_analytics_${new Date().toISOString().split('T')[0]}.json`\n    link.click()\n    URL.revokeObjectURL(url)\n  }\n\n\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {/* Header Skeleton */}\n        <div className=\"flex justify-between items-center\">\n          <div\n            className=\"w-64 h-10 rounded-lg\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',\n              backgroundImage: resolvedTheme === 'dark'\n                ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'\n                : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',\n              backgroundSize: '200% 100%',\n              animation: 'shimmer 2s infinite'\n            }}\n          />\n          <div\n            className=\"w-48 h-10 rounded-lg\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',\n              backgroundImage: resolvedTheme === 'dark'\n                ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'\n                : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',\n              backgroundSize: '200% 100%',\n              animation: 'shimmer 2s infinite'\n            }}\n          />\n        </div>\n\n        {/* Debts List Skeleton */}\n        <LoadingSkeleton type=\"debts\" count={4} />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Enhanced Header with Advanced Controls */}\n      <div className=\"space-y-4\">\n        {/* Top Row - Search and Actions */}\n        <div className=\"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4\">\n          {/* Search Section */}\n          <div className=\"flex flex-col sm:flex-row gap-3 flex-1\">\n            <div className=\"relative flex-1 max-w-md\">\n              <Search\n                className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'\n                }}\n              />\n              <input\n                type=\"text\"\n                placeholder=\"Search customers, products, or amounts...\"\n                value={searchTerm}\n                onChange={(e) => handleSearchChange(e.target.value)}\n                onFocus={() => setShowSuggestions(searchTerm.length >= 2)}\n                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}\n                className=\"w-full pl-10 pr-4 py-2.5 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 shadow-sm\"\n                style={{\n                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                }}\n              />\n\n              {/* Search Suggestions */}\n              {showSuggestions && searchSuggestions.length > 0 && (\n                <div\n                  className=\"absolute top-full left-0 right-0 mt-1 rounded-lg shadow-lg border z-50 max-h-48 overflow-y-auto\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db'\n                  }}\n                >\n                  {searchSuggestions.map((suggestion, index) => (\n                    <button\n                      key={index}\n                      onClick={() => handleSuggestionClick(suggestion)}\n                      className=\"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                      }}\n                    >\n                      <Users className=\"h-4 w-4 inline mr-2 text-green-600\" />\n                      {suggestion}\n                    </button>\n                  ))}\n                </div>\n              )}\n            </div>\n\n            {/* Quick Filters */}\n            <div className=\"flex gap-2\">\n              <button\n                onClick={() => setFilters(prev => ({ ...prev, status: prev.status === 'recent' ? 'all' : 'recent' }))}\n                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${\n                  filters.status === 'recent'\n                    ? 'bg-blue-100 text-blue-700 border-blue-300'\n                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'\n                } border`}\n              >\n                <Clock className=\"h-4 w-4 inline mr-1\" />\n                Recent\n              </button>\n              <button\n                onClick={() => setFilters(prev => ({ ...prev, status: prev.status === 'overdue' ? 'all' : 'overdue' }))}\n                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${\n                  filters.status === 'overdue'\n                    ? 'bg-red-100 text-red-700 border-red-300'\n                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'\n                } border`}\n              >\n                <AlertTriangle className=\"h-4 w-4 inline mr-1\" />\n                Overdue\n              </button>\n              <button\n                onClick={() => setFilters(prev => ({ ...prev, status: prev.status === 'high-amount' ? 'all' : 'high-amount' }))}\n                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${\n                  filters.status === 'high-amount'\n                    ? 'bg-orange-100 text-orange-700 border-orange-300'\n                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'\n                } border`}\n              >\n                <DollarSign className=\"h-4 w-4 inline mr-1\" />\n                High Amount\n              </button>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex gap-2\">\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className={`px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm ${\n                showFilters\n                  ? 'bg-green-100 text-green-700 border-green-300'\n                  : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'\n              } border`}\n            >\n              <Filter className=\"h-4 w-4 inline mr-2\" />\n              Filters\n            </button>\n\n            {/* Export Dropdown */}\n            {filteredAndSortedDebts.length > 0 && (\n              <div className=\"relative group\">\n                <button\n                  className=\"px-4 py-2.5 rounded-xl font-medium transition-all duration-300 shadow-sm bg-blue-100 text-blue-700 border-blue-300 hover:bg-blue-200 border\"\n                >\n                  <Download className=\"h-4 w-4 inline mr-2\" />\n                  Export\n                  <ChevronDown className=\"h-4 w-4 inline ml-1\" />\n                </button>\n\n                <div className=\"absolute right-0 top-full mt-1 w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div\n                    className=\"rounded-lg shadow-lg border py-1\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #e5e7eb'\n                    }}\n                  >\n                    <button\n                      onClick={handleExportCSV}\n                      className=\"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                      }}\n                    >\n                      <Download className=\"h-4 w-4 text-green-600\" />\n                      <span className=\"text-sm font-medium\">Export as CSV</span>\n                    </button>\n                    <button\n                      onClick={handleExportJSON}\n                      className=\"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                      }}\n                    >\n                      <Download className=\"h-4 w-4 text-blue-600\" />\n                      <span className=\"text-sm font-medium\">Export as JSON</span>\n                    </button>\n                    <div\n                      className=\"my-1 h-px\"\n                      style={{\n                        backgroundColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'\n                      }}\n                    />\n                    <button\n                      onClick={handleExportAnalytics}\n                      className=\"w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3\"\n                      style={{\n                        color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                      }}\n                    >\n                      <BarChart3 className=\"h-4 w-4 text-purple-600\" />\n                      <span className=\"text-sm font-medium\">Analytics Report</span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <button\n              onClick={() => setIsModalOpen(true)}\n              className=\"flex items-center px-6 py-2.5 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg font-medium\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Debt Record\n            </button>\n          </div>\n        </div>\n\n        {/* Advanced Filters Panel */}\n        {showFilters && (\n          <div\n            className=\"p-6 rounded-xl border shadow-sm animate-slide-down\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f8fafc',\n              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #e2e8f0'\n            }}\n          >\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {/* Date Range Filter */}\n              <div>\n                <label className=\"block text-sm font-medium mb-2\" style={{\n                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                }}>\n                  Date Range\n                </label>\n                <div className=\"flex gap-2\">\n                  <input\n                    type=\"date\"\n                    value={filters.dateRange.from}\n                    onChange={(e) => setFilters(prev => ({\n                      ...prev,\n                      dateRange: { ...prev.dateRange, from: e.target.value }\n                    }))}\n                    className=\"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                    }}\n                  />\n                  <input\n                    type=\"date\"\n                    value={filters.dateRange.to}\n                    onChange={(e) => setFilters(prev => ({\n                      ...prev,\n                      dateRange: { ...prev.dateRange, to: e.target.value }\n                    }))}\n                    className=\"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                    }}\n                  />\n                </div>\n              </div>\n\n              {/* Amount Range */}\n              <div>\n                <label className=\"block text-sm font-medium mb-2\" style={{\n                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                }}>\n                  Amount Range (₱)\n                </label>\n                <div className=\"flex gap-2\">\n                  <input\n                    type=\"number\"\n                    placeholder=\"Min\"\n                    value={filters.amountRange.min}\n                    onChange={(e) => setFilters(prev => ({\n                      ...prev,\n                      amountRange: { ...prev.amountRange, min: Number(e.target.value) || 0 }\n                    }))}\n                    className=\"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                    }}\n                  />\n                  <input\n                    type=\"number\"\n                    placeholder=\"Max\"\n                    value={filters.amountRange.max}\n                    onChange={(e) => setFilters(prev => ({\n                      ...prev,\n                      amountRange: { ...prev.amountRange, max: Number(e.target.value) || 50000 }\n                    }))}\n                    className=\"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm\"\n                    style={{\n                      backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                      border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                    }}\n                  />\n                </div>\n              </div>\n\n              {/* Customer Filter */}\n              <div>\n                <label className=\"block text-sm font-medium mb-2\" style={{\n                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                }}>\n                  Customer\n                </label>\n                <input\n                  type=\"text\"\n                  placeholder=\"Filter by customer name\"\n                  value={filters.customer}\n                  onChange={(e) => setFilters(prev => ({ ...prev, customer: e.target.value }))}\n                  className=\"w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 text-sm\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                    border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                  }}\n                />\n              </div>\n\n              {/* Reset Filters */}\n              <div className=\"flex items-end\">\n                <button\n                  onClick={() => setFilters({\n                    search: '',\n                    dateRange: { from: '', to: '' },\n                    amountRange: { min: 0, max: 50000 },\n                    status: 'all',\n                    customer: ''\n                  })}\n                  className=\"w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-300 font-medium text-sm\"\n                >\n                  <RefreshCw className=\"h-4 w-4 inline mr-2\" />\n                  Reset Filters\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Toolbar - Sort, View Mode, Bulk Actions */}\n        <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4\">\n          {/* Left Side - Results Info & Bulk Actions */}\n          <div className=\"flex items-center gap-4\">\n            <span className=\"text-sm font-medium\" style={{\n              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n            }}>\n              {viewMode === 'customer'\n                ? `${customerSummaries.length} customer${customerSummaries.length !== 1 ? 's' : ''} with ${filteredAndSortedDebts.length} debt${filteredAndSortedDebts.length !== 1 ? 's' : ''}`\n                : `${filteredAndSortedDebts.length} debt record${filteredAndSortedDebts.length !== 1 ? 's' : ''} found`\n              }\n            </span>\n\n            {/* Bulk Selection */}\n            {filteredAndSortedDebts.length > 0 && viewMode !== 'customer' && (\n              <div className=\"flex items-center gap-2\">\n                <button\n                  onClick={handleSelectAll}\n                  className=\"p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                  title={selectedDebts.size === filteredAndSortedDebts.length ? 'Deselect All' : 'Select All'}\n                >\n                  {selectedDebts.size === filteredAndSortedDebts.length ? (\n                    <CheckSquare className=\"h-4 w-4 text-green-600\" />\n                  ) : selectedDebts.size > 0 ? (\n                    <Minus className=\"h-4 w-4 text-gray-600\" />\n                  ) : (\n                    <Square className=\"h-4 w-4 text-gray-600\" />\n                  )}\n                </button>\n\n                {selectedDebts.size > 0 && (\n                  <div className=\"flex items-center gap-2\">\n                    <span className=\"text-sm text-green-600 font-medium\">\n                      {selectedDebts.size} selected\n                    </span>\n                    <button\n                      onClick={handleBulkDelete}\n                      className=\"px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-sm font-medium\"\n                    >\n                      <Trash2 className=\"h-3 w-3 inline mr-1\" />\n                      Delete\n                    </button>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Right Side - Sort & View Controls */}\n          <div className=\"flex items-center gap-3\">\n            {/* Sort Controls */}\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-sm font-medium\" style={{\n                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n              }}>\n                Sort by:\n              </span>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value as DebtSortOption)}\n                className=\"px-3 py-1.5 rounded-lg text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300\"\n                style={{\n                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',\n                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',\n                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'\n                }}\n              >\n                <option value=\"debt_date\">Debt Date</option>\n                <option value=\"customer_name\">Customer Name</option>\n                <option value=\"total_amount\">Amount</option>\n                <option value=\"product_name\">Product</option>\n                <option value=\"created_at\">Date Added</option>\n              </select>\n              <button\n                onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}\n                className=\"p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n                title={`Sort ${sortDirection === 'asc' ? 'Descending' : 'Ascending'}`}\n              >\n                {sortDirection === 'asc' ? (\n                  <SortAsc className=\"h-4 w-4\" style={{\n                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                  }} />\n                ) : (\n                  <SortDesc className=\"h-4 w-4\" style={{\n                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                  }} />\n                )}\n              </button>\n            </div>\n\n            {/* View Mode Toggle */}\n            <div className=\"flex rounded-lg border\" style={{\n              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db'\n            }}>\n              <button\n                onClick={() => setViewMode('customer')}\n                className={`px-3 py-2 text-sm transition-colors ${\n                  viewMode === 'customer'\n                    ? 'bg-green-100 text-green-700'\n                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n                title=\"Customer View\"\n              >\n                <Users className=\"h-4 w-4 inline mr-1\" />\n                Customers\n              </button>\n              <button\n                onClick={() => setViewMode('list')}\n                className={`px-3 py-2 text-sm transition-colors ${\n                  viewMode === 'list'\n                    ? 'bg-green-100 text-green-700'\n                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n                title=\"List View\"\n              >\n                <List className=\"h-4 w-4 inline mr-1\" />\n                List\n              </button>\n              <button\n                onClick={() => setViewMode('grid')}\n                className={`px-3 py-2 text-sm transition-colors ${\n                  viewMode === 'grid'\n                    ? 'bg-green-100 text-green-700'\n                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n                title=\"Grid View\"\n              >\n                <Grid className=\"h-4 w-4 inline mr-1\" />\n                Grid\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Enhanced Debts Display */}\n      <div className=\"space-y-6\">\n        {viewMode === 'customer' ? (\n          // Customer-grouped view\n          customerSummaries.length > 0 ? (\n            customerSummaries.map((customer) => (\n              <div\n                key={customer.customerName}\n                className=\"rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg\"\n                style={{\n                  backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                  border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n                }}\n              >\n                {/* Customer Header */}\n                <div\n                  className=\"px-6 py-4 border-b\"\n                  style={{\n                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f8fafc',\n                    borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e2e8f0'\n                  }}\n                >\n                  <div className=\"flex justify-between items-center\">\n                    <div className=\"flex items-center gap-4\">\n                      <CustomerAvatar\n                        key={`${customer.customerName}-${avatarRefreshKey}`}\n                        customerName={customer.customerName.split(' ')[0]}\n                        customerFamilyName={customer.customerName.split(' ').slice(1).join(' ')}\n                        profilePictureUrl={customerProfiles.get(customer.customerName)?.profile_picture_url}\n                        size=\"lg\"\n                        showEditButton={true}\n                        onEditClick={() => handleEditProfile(\n                          customer.customerName.split(' ')[0],\n                          customer.customerName.split(' ').slice(1).join(' ')\n                        )}\n                      />\n                      <div>\n                        <h3\n                          className=\"text-lg font-semibold\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                          }}\n                        >\n                          {customer.customerName}\n                        </h3>\n                        <p\n                          className=\"text-sm\"\n                          style={{\n                            color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                          }}\n                        >\n                          {customer.totalDebts} debt record{customer.totalDebts !== 1 ? 's' : ''}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"space-y-2\">\n                        <div>\n                          <p className=\"text-lg font-semibold text-gray-600\">\n                            ₱{customer.totalAmount.toFixed(2)}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">Total Debt</p>\n                        </div>\n\n                        {customer.totalPayments > 0 && (\n                          <div>\n                            <p className=\"text-lg font-semibold text-green-600\">\n                              -₱{customer.totalPayments.toFixed(2)}\n                            </p>\n                            <p className=\"text-xs text-gray-500\">Total Paid</p>\n                          </div>\n                        )}\n\n                        <div className=\"border-t pt-2\">\n                          <p className={`text-2xl font-bold ${\n                            customer.remainingBalance <= 0 ? 'text-green-600' : 'text-red-600'\n                          }`}>\n                            ₱{customer.remainingBalance.toFixed(2)}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">\n                            {customer.remainingBalance <= 0 ? 'Fully Paid' : 'Balance Due'}\n                          </p>\n                        </div>\n\n                        {customer.remainingBalance > 0 && (\n                          <button\n                            onClick={() => {\n                              const nameParts = customer.customerName.split(' ')\n                              const firstName = nameParts[0] || ''\n                              const lastName = nameParts.slice(1).join(' ') || ''\n                              handleMakePayment(firstName, lastName)\n                            }}\n                            className=\"w-full mt-2 px-3 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg text-sm font-medium\"\n                          >\n                            <DollarSign className=\"h-4 w-4 inline mr-1\" />\n                            Record Payment\n                          </button>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Customer Stats */}\n                  <div className=\"mt-4 grid grid-cols-2 gap-4\">\n                    <div className=\"flex items-center gap-2\">\n                      <Clock className=\"h-4 w-4 text-blue-500\" />\n                      <span\n                        className=\"text-sm\"\n                        style={{\n                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                        }}\n                      >\n                        Oldest: {format(new Date(customer.oldestDebt), 'MMM dd, yyyy')}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <Calendar className=\"h-4 w-4 text-green-500\" />\n                      <span\n                        className=\"text-sm\"\n                        style={{\n                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                        }}\n                      >\n                        Recent: {format(new Date(customer.recentDebt), 'MMM dd, yyyy')}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Debt Records */}\n                <div className=\"divide-y\" style={{\n                  borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'\n                }}>\n                  {customer.debts.map((debt) => (\n                    <div key={debt.id} className=\"px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                      <div className=\"flex justify-between items-start\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center gap-3 mb-2\">\n                            <div className=\"p-1.5 rounded-lg bg-blue-100\">\n                              <Package className=\"h-4 w-4 text-blue-600\" />\n                            </div>\n                            <h4\n                              className=\"font-medium\"\n                              style={{\n                                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                              }}\n                            >\n                              {debt.product_name}\n                            </h4>\n                          </div>\n\n                          <div className=\"ml-8 space-y-1\">\n                            <div className=\"flex items-center gap-4 text-sm\" style={{\n                              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                            }}>\n                              <span className=\"flex items-center gap-1\">\n                                <Calculator className=\"h-3 w-3\" />\n                                Qty: {debt.quantity}\n                              </span>\n                              <span className=\"flex items-center gap-1\">\n                                <DollarSign className=\"h-3 w-3\" />\n                                Unit: ₱{debt.product_price.toFixed(2)}\n                              </span>\n                            </div>\n                            <div className=\"flex items-center gap-1 text-sm\" style={{\n                              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                            }}>\n                              <Calendar className=\"h-3 w-3\" />\n                              <span>{format(new Date(debt.debt_date), 'MMM dd, yyyy')}</span>\n                              <span className=\"mx-2\">•</span>\n                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                                differenceInDays(new Date(), new Date(debt.debt_date)) > 30\n                                  ? 'bg-red-100 text-red-700'\n                                  : differenceInDays(new Date(), new Date(debt.debt_date)) > 7\n                                    ? 'bg-orange-100 text-orange-700'\n                                    : 'bg-green-100 text-green-700'\n                              }`}>\n                                {differenceInDays(new Date(), new Date(debt.debt_date))} days ago\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center gap-3 ml-4\">\n                          <div className=\"text-right\">\n                            <p className=\"text-lg font-bold text-red-600\">\n                              ₱{debt.total_amount.toFixed(2)}\n                            </p>\n                          </div>\n\n                          <div className=\"flex gap-1\">\n                            <button\n                              onClick={() => handleEdit(debt)}\n                              className=\"p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-all duration-200 hover:scale-105\"\n                              title=\"Edit Debt Record\"\n                            >\n                              <Edit className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              onClick={() => handleDelete(debt.id)}\n                              className=\"p-2 text-red-600 hover:bg-red-100 rounded-lg transition-all duration-200 hover:scale-105\"\n                              title=\"Delete Debt Record\"\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))\n          ) : (\n            <div\n              className=\"text-center py-16 rounded-xl border-2 border-dashed transition-all duration-300\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.5)' : 'rgba(249, 250, 251, 0.8)',\n                borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n              }}\n            >\n              <div\n                className=\"w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 transition-all duration-300\"\n                style={{\n                  backgroundColor: resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.05)',\n                  border: resolvedTheme === 'dark' ? '2px solid rgba(34, 197, 94, 0.3)' : '2px solid rgba(34, 197, 94, 0.2)'\n                }}\n              >\n                <Users\n                  className=\"h-10 w-10 transition-colors duration-300\"\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a'\n                  }}\n                />\n              </div>\n              <h3\n                className=\"text-xl font-semibold mb-3 transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                }}\n              >\n                No customers with debt records found\n              </h3>\n              <p\n                className=\"text-sm mb-6 max-w-md mx-auto transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280'\n                }}\n              >\n                {searchTerm || Object.values(filters).some(f => f && f !== 'all')\n                  ? 'Try adjusting your search terms or filter criteria to find what you\\'re looking for'\n                  : 'Get started by adding your first debt record to track customer purchases'}\n              </p>\n              {!searchTerm && !Object.values(filters).some(f => f && f !== 'all') && (\n                <button\n                  onClick={() => setIsModalOpen(true)}\n                  className=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg font-medium\"\n                >\n                  <Plus className=\"h-5 w-5 mr-2\" />\n                  Add First Debt Record\n                </button>\n              )}\n            </div>\n          )\n        ) : (\n          // List and Grid views\n          <div className={viewMode === 'grid'\n            ? \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\n            : \"space-y-4\"\n          }>\n            {filteredAndSortedDebts.map((debt) => (\n              <div\n                key={debt.id}\n                className={`relative group rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl ${\n                  selectedDebts.has(debt.id)\n                    ? 'ring-2 ring-green-500 ring-offset-2'\n                    : 'hover:scale-[1.02]'\n                } ${viewMode === 'list' ? 'flex items-center' : ''}`}\n                style={{\n                  backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',\n                  border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'\n                }}\n              >\n                {/* Selection Checkbox */}\n                <div className=\"absolute top-3 left-3 z-10\">\n                  <button\n                    onClick={() => handleSelectDebt(debt.id)}\n                    className={`p-1 rounded-md transition-all duration-200 ${\n                      selectedDebts.has(debt.id)\n                        ? 'bg-green-500 text-white'\n                        : 'bg-white/80 text-gray-600 hover:bg-white'\n                    } shadow-sm`}\n                  >\n                    {selectedDebts.has(debt.id) ? (\n                      <CheckSquare className=\"h-4 w-4\" />\n                    ) : (\n                      <Square className=\"h-4 w-4\" />\n                    )}\n                  </button>\n                </div>\n\n                {/* Debt Content */}\n                <div className={`${viewMode === 'grid' ? 'p-6' : 'flex-1 p-4'}`}>\n                  <div className={`${viewMode === 'list' ? 'flex items-center justify-between' : ''}`}>\n                    <div className={`${viewMode === 'list' ? 'flex-1' : ''}`}>\n                      {/* Customer Info */}\n                      <div className=\"flex items-center gap-3 mb-3\">\n                        <CustomerAvatar\n                          key={`${debt.customer_name}-${debt.customer_family_name}-${avatarRefreshKey}`}\n                          customerName={debt.customer_name}\n                          customerFamilyName={debt.customer_family_name}\n                          profilePictureUrl={customerProfiles.get(`${debt.customer_name} ${debt.customer_family_name}`)?.profile_picture_url}\n                          size=\"md\"\n                          showEditButton={false}\n                        />\n                        <div>\n                          <h3\n                            className=\"font-semibold\"\n                            style={{\n                              color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n                            }}\n                          >\n                            {debt.customer_name} {debt.customer_family_name}\n                          </h3>\n                          <p\n                            className=\"text-sm\"\n                            style={{\n                              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                            }}\n                          >\n                            {debt.product_name}\n                          </p>\n                        </div>\n                      </div>\n\n                      {/* Debt Details */}\n                      <div className={`${viewMode === 'list' ? 'flex items-center gap-6' : 'space-y-2'}`}>\n                        <div className=\"flex items-center gap-4 text-sm\" style={{\n                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                        }}>\n                          <span className=\"flex items-center gap-1\">\n                            <Calculator className=\"h-3 w-3\" />\n                            Qty: {debt.quantity}\n                          </span>\n                          <span className=\"flex items-center gap-1\">\n                            <DollarSign className=\"h-3 w-3\" />\n                            ₱{debt.product_price.toFixed(2)}\n                          </span>\n                        </div>\n\n                        <div className=\"flex items-center gap-1 text-sm\" style={{\n                          color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                        }}>\n                          <Calendar className=\"h-3 w-3\" />\n                          <span>{format(new Date(debt.debt_date), 'MMM dd, yyyy')}</span>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <span className=\"text-2xl font-bold text-red-600\">\n                            ₱{debt.total_amount.toFixed(2)}\n                          </span>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                            differenceInDays(new Date(), new Date(debt.debt_date)) > 30\n                              ? 'bg-red-100 text-red-700'\n                              : differenceInDays(new Date(), new Date(debt.debt_date)) > 7\n                                ? 'bg-orange-100 text-orange-700'\n                                : 'bg-green-100 text-green-700'\n                          }`}>\n                            {differenceInDays(new Date(), new Date(debt.debt_date))} days ago\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Action Buttons */}\n                    <div className={`${viewMode === 'list' ? 'flex gap-2 ml-4' : 'flex gap-2 mt-4'}`}>\n                      <button\n                        onClick={() => handleEdit(debt)}\n                        className=\"flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-all duration-300 hover:scale-105 font-medium\"\n                        title=\"Edit Debt Record\"\n                      >\n                        <Edit className=\"h-4 w-4 mr-1\" />\n                        {viewMode === 'grid' ? 'Edit' : ''}\n                      </button>\n                      <button\n                        onClick={() => handleDelete(debt.id)}\n                        className=\"flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-all duration-300 hover:scale-105 font-medium\"\n                        title=\"Delete Debt Record\"\n                      >\n                        <Trash2 className=\"h-4 w-4 mr-1\" />\n                        {viewMode === 'grid' ? 'Delete' : ''}\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n\n        {/* Empty State for List/Grid Views */}\n        {(viewMode === 'list' || viewMode === 'grid') && filteredAndSortedDebts.length === 0 && !loading && (\n          <div\n            className=\"text-center py-16 rounded-xl border-2 border-dashed transition-all duration-300\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.5)' : 'rgba(249, 250, 251, 0.8)',\n              borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'\n            }}\n          >\n            <div\n              className=\"w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 transition-all duration-300\"\n              style={{\n                backgroundColor: resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.05)',\n                border: resolvedTheme === 'dark' ? '2px solid rgba(34, 197, 94, 0.3)' : '2px solid rgba(34, 197, 94, 0.2)'\n              }}\n            >\n              <FileText\n                className=\"h-10 w-10 transition-colors duration-300\"\n                style={{\n                  color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a'\n                }}\n              />\n            </div>\n            <h3\n              className=\"text-xl font-semibold mb-3 transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'\n              }}\n            >\n              {searchTerm || Object.values(filters).some(f => f && f !== 'all') ? 'No debt records found' : 'No debt records yet'}\n            </h3>\n            <p\n              className=\"text-sm mb-6 max-w-md mx-auto transition-colors duration-300\"\n              style={{\n                color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280'\n              }}\n            >\n              {searchTerm || Object.values(filters).some(f => f && f !== 'all')\n                ? 'Try adjusting your search criteria or filters to find specific debt records'\n                : 'Start tracking customer debts by adding your first debt record to manage store credit'}\n            </p>\n            {!searchTerm && !Object.values(filters).some(f => f && f !== 'all') && (\n              <button\n                onClick={() => setIsModalOpen(true)}\n                className=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl\"\n              >\n                <Plus className=\"h-5 w-5 mr-2\" />\n                Add First Debt Record\n              </button>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Debt Modal */}\n      <DebtModal\n        isOpen={isModalOpen}\n        onClose={handleModalClose}\n        debt={editingDebt}\n      />\n\n      {/* Payment Modal */}\n      <PaymentModal\n        isOpen={isPaymentModalOpen}\n        onClose={handlePaymentModalClose}\n        customerName={selectedCustomerForPayment?.name || ''}\n        customerFamilyName={selectedCustomerForPayment?.familyName || ''}\n        currentBalance={selectedCustomerForPayment?.balance || 0}\n        payment={editingPayment}\n        profilePictureUrl={\n          selectedCustomerForPayment\n            ? customerProfiles.get(`${selectedCustomerForPayment.name} ${selectedCustomerForPayment.familyName}`)?.profile_picture_url\n            : undefined\n        }\n      />\n\n      {/* Customer Profile Modal */}\n      <CustomerProfileModal\n        isOpen={isProfileModalOpen}\n        onClose={handleProfileModalClose}\n        customer={selectedCustomerForProfile?.customer || null}\n        customerName={selectedCustomerForProfile?.name || ''}\n        customerFamilyName={selectedCustomerForProfile?.familyName || ''}\n        onProfileUpdated={handleProfileUpdated}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AACA;AAGA;AAEA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;;AAiDe,SAAS,aAAa,EAAE,aAAa,EAAqB;IACvE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAEpE,0BAA0B;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QACxD,QAAQ;QACR,WAAW;YAAE,MAAM;YAAI,IAAI;QAAG;QAC9B,aAAa;YAAE,KAAK;YAAG,KAAK;QAAM;QAClC,QAAQ;QACR,UAAU;IACZ;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,wBAAwB;IACxB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIjE;IACV,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC,IAAI;IAC3F,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAE7E,iCAAiC;IACjC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIjE;IACV,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,IAAI;IACpF,MAAM,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,qBAAkB,AAAD;IAEvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;QACA;IACF,GAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,aAAa,IAAI;gBACvB,iDAAiD;gBACjD,IAAI,KAAK,QAAQ,IAAI,MAAM,OAAO,CAAC,KAAK,QAAQ,GAAG;oBACjD,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC;wBACrB,MAAM,MAAM,GAAG,QAAQ,aAAa,CAAC,CAAC,EAAE,QAAQ,oBAAoB,EAAE;wBACtE,WAAW,GAAG,CAAC,KAAK;oBACtB;gBACF;gBACA,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,4CAA4C;YAC5C,oBAAoB,IAAI;QAC1B;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,aAAa,IAAI;gBACvB,kDAAkD;gBAClD,IAAI,KAAK,SAAS,IAAI,MAAM,OAAO,CAAC,KAAK,SAAS,GAAG;oBACnD,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC;wBACtB,MAAM,MAAM,GAAG,SAAS,aAAa,CAAC,CAAC,EAAE,SAAS,oBAAoB,EAAE;wBACxE,WAAW,GAAG,CAAC,KAAK;oBACtB;gBACF;gBACA,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,4CAA4C;YAC5C,oBAAoB,IAAI;QAC1B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,mEAAmE;YACnE,QAAQ,IAAI,CAAC;YACb,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,IAAI,CAAC,0BAA0B;YAEvC,sEAAsE;YACtE,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE;gBAChD,SAAS,KAAK,IAAI,CAAC,KAAK;gBACxB,QAAQ,IAAI,CAAC,mCAAmC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAC1E,OAEK,IAAI,KAAK,KAAK,EAAE;gBACnB,SAAS,KAAK,KAAK;gBACnB,QAAQ,IAAI,CAAC,mCAAmC,KAAK,KAAK,CAAC,MAAM,EAAE;YACrE,OAEK,IAAI,MAAM,OAAO,CAAC,OAAO;gBAC5B,SAAS;gBACT,QAAQ,IAAI,CAAC,kCAAkC,KAAK,MAAM,EAAE;YAC9D,OACK;gBACH,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,SAAS,EAAE;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS,EAAE;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,sDAAsD;QAEnE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE;gBAC/C,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACpC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,eAAe;QACf;QACA;QACA;IACF;IAEA,MAAM,0BAA0B;QAC9B,sBAAsB;QACtB,8BAA8B;QAC9B,kBAAkB;QAClB;QACA;IACF;IAEA,MAAM,oBAAoB,CAAC,cAAsB;QAC/C,MAAM,cAAc,GAAG,aAAa,CAAC,EAAE,oBAAoB;QAC3D,MAAM,UAAU,iBAAiB,GAAG,CAAC;QAErC,8BAA8B;YAC5B,MAAM;YACN,YAAY;YACZ,SAAS,SAAS,qBAAqB;QACzC;QACA,sBAAsB;IACxB;IAEA,MAAM,0BAA0B;QAC9B,sBAAsB;QACtB,8BAA8B;QAC9B,iDAAiD;QACjD;QACA;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,iDAAiD;QACjD,sBAAsB,gBAAgB,aAAa,EAAE,gBAAgB,oBAAoB,EAAE;QAE3F,4BAA4B;QAC5B,MAAM,MAAM,GAAG,gBAAgB,aAAa,CAAC,CAAC,EAAE,gBAAgB,oBAAoB,EAAE;QACtF,oBAAoB,CAAA,OAAQ,IAAI,IAAI,KAAK,GAAG,CAAC,KAAK;QAElD,+CAA+C;QAC/C,oBAAoB,CAAA,OAAQ,OAAO;QAEnC,mCAAmC;QACnC;QACA;IACF;IAEA,MAAM,oBAAoB,OAAO,cAAsB;QACrD,MAAM,WAAW,MAAM,mBAAmB,cAAc;QACxD,8BAA8B;YAC5B,MAAM;YACN,YAAY;YACZ;QACF;QACA,sBAAsB;IACxB;IAEA,8BAA8B;IAC9B,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7C,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;YAC5B,qBAAqB,EAAE;YACvB;QACF;QAEA,MAAM,cAAc,MACjB,MAAM,CAAC,CAAA;YACN,MAAM,eAAe,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE,CAAC,WAAW;YACrF,MAAM,cAAc,KAAK,YAAY,CAAC,WAAW;YACjD,OAAO,aAAa,QAAQ,CAAC,KAAK,WAAW,OAAO,YAAY,QAAQ,CAAC,KAAK,WAAW;QAC3F,GACC,GAAG,CAAC,CAAA,OAAQ,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE,EAChE,KAAK,CAAC,GAAG;QAEZ,qBAAqB;eAAI,IAAI,IAAI;SAAa;IAChD,GAAG;QAAC;KAAM;IAEV,uCAAuC;IACvC,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACrC,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA;YAC5B,cAAc;YACd,MAAM,eAAe,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE,CAAC,WAAW;YACrF,MAAM,cAAc,KAAK,YAAY,CAAC,WAAW;YACjD,MAAM,aAAa,CAAC,QAAQ,MAAM,IAAI,UAAU,EAAE,WAAW;YAC7D,MAAM,gBAAgB,CAAC,cACrB,aAAa,QAAQ,CAAC,eACtB,YAAY,QAAQ,CAAC;YAEvB,oBAAoB;YACpB,MAAM,WAAW,IAAI,KAAK,KAAK,SAAS;YACxC,MAAM,mBAAmB,CAAC,CAAC,QAAQ,SAAS,CAAC,IAAI,IAAI,YAAY,IAAI,KAAK,QAAQ,SAAS,CAAC,IAAI,CAAC,KAC/F,CAAC,CAAC,QAAQ,SAAS,CAAC,EAAE,IAAI,YAAY,IAAI,KAAK,QAAQ,SAAS,CAAC,EAAE,CAAC;YAEtE,sBAAsB;YACtB,MAAM,gBAAgB,KAAK,YAAY,IAAI,QAAQ,WAAW,CAAC,GAAG,IAChE,KAAK,YAAY,IAAI,QAAQ,WAAW,CAAC,GAAG;YAE9C,gBAAgB;YAChB,IAAI,gBAAgB;YACpB,IAAI,QAAQ,MAAM,KAAK,OAAO;gBAC5B,MAAM,WAAW,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,QAAQ;gBAC9C,OAAQ,QAAQ,MAAM;oBACpB,KAAK;wBACH,gBAAgB,YAAY;wBAC5B;oBACF,KAAK;wBACH,gBAAgB,WAAW;wBAC3B;oBACF,KAAK;wBACH,gBAAgB,KAAK,YAAY,GAAG;wBACpC;gBACJ;YACF;YAEA,kBAAkB;YAClB,MAAM,kBAAkB,CAAC,QAAQ,QAAQ,IACvC,aAAa,QAAQ,CAAC,QAAQ,QAAQ,CAAC,WAAW;YAEpD,OAAO,iBAAiB,oBAAoB,iBAAiB,iBAAiB;QAChF;QAEA,UAAU;QACV,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,IAAI,QAAyB;YAE7B,OAAQ;gBACN,KAAK;oBACH,SAAS,GAAG,EAAE,aAAa,CAAC,CAAC,EAAE,EAAE,oBAAoB,EAAE,CAAC,WAAW;oBACnE,SAAS,GAAG,EAAE,aAAa,CAAC,CAAC,EAAE,EAAE,oBAAoB,EAAE,CAAC,WAAW;oBACnE;gBACF,KAAK;oBACH,SAAS,EAAE,YAAY;oBACvB,SAAS,EAAE,YAAY;oBACvB;gBACF,KAAK;oBACH,SAAS,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;oBACtC,SAAS,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;oBACtC;gBACF,KAAK;oBACH,SAAS,EAAE,YAAY,CAAC,WAAW;oBACnC,SAAS,EAAE,YAAY,CAAC,WAAW;oBACnC;gBACF,KAAK;oBACH,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;oBACvC,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;oBACvC;gBACF;oBACE,OAAO;YACX;YAEA,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,CAAC,IAAI;YAC3D,IAAI,SAAS,QAAQ,OAAO,kBAAkB,QAAQ,IAAI,CAAC;YAC3D,OAAO;QACT;QAEA,OAAO;IACT,GAAG;QAAC;QAAO;QAAY;QAAS;QAAQ;KAAc;IAEtD,0CAA0C;IAC1C,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAChC,MAAM,YAA+B,EAAE;QACvC,MAAM,cAAc,IAAI;QAExB,uBAAuB,OAAO,CAAC,CAAA;YAC7B,MAAM,cAAc,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE;YACxE,IAAI,CAAC,YAAY,GAAG,CAAC,cAAc;gBACjC,YAAY,GAAG,CAAC,aAAa,EAAE;YACjC;YACA,YAAY,GAAG,CAAC,aAAc,IAAI,CAAC;QACrC;QAEA,YAAY,OAAO,CAAC,CAAC,OAAO;YAC1B,MAAM,cAAc,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;YACzE,MAAM,cAAc,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YAExG,0BAA0B;YAC1B,MAAM,UAAU,iBAAiB,GAAG,CAAC;YACrC,MAAM,gBAAgB,SAAS,kBAAkB;YACjD,MAAM,mBAAmB,SAAS,qBAAqB;YAEvD,UAAU,IAAI,CAAC;gBACb;gBACA,YAAY,MAAM,MAAM;gBACxB;gBACA;gBACA;gBACA,YAAY,WAAW,CAAC,EAAE,EAAE,aAAa;gBACzC,YAAY,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,EAAE,aAAa;gBAC9D,OAAO;YACT;QACF;QAEA,OAAO,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;IAC/D,GAAG;QAAC;QAAwB;KAAiB;IAE7C,2BAA2B;IAC3B,MAAM,kBAAkB;QACtB,IAAI,cAAc,IAAI,KAAK,uBAAuB,MAAM,EAAE;YACxD,iBAAiB,IAAI;QACvB,OAAO;YACL,iBAAiB,IAAI,IAAI,uBAAuB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAC/D;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,SAAS;YAC3B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,iBAAiB;IACnB;IAEA,MAAM,mBAAmB;QACvB,IAAI,cAAc,IAAI,KAAK,GAAG;QAE9B,MAAM,iBAAiB,CAAC,gCAAgC,EAAE,cAAc,IAAI,CAAC,gBAAgB,CAAC;QAC9F,IAAI,CAAC,QAAQ,iBAAiB;QAE9B,IAAI;YACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,eAAe,GAAG,CAAC,CAAA,KACnD,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE;oBAAE,QAAQ;gBAAS;YAG/C,MAAM,QAAQ,GAAG,CAAC;YAClB,iBAAiB,IAAI;YACrB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,kBAAkB;IAClB,MAAM,qBAAqB,CAAC;QAC1B,cAAc;QACd,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;YAAM,CAAC;QAC9C,0BAA0B;QAC1B,mBAAmB,MAAM,MAAM,IAAI;IACrC;IAEA,MAAM,wBAAwB,CAAC;QAC7B,cAAc;QACd,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;YAAW,CAAC;QACnD,mBAAmB;IACrB;IAIA,kBAAkB;IAClB,MAAM,kBAAkB;QACtB,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,wBAAwB,CAAC,eAAe,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;IACrG;IAEA,MAAM,mBAAmB;QACvB,MAAM,cAAc,KAAK,SAAS,CAAC,wBAAwB,MAAM;QACjE,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAAmB;QAChE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,CAAC,eAAe,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;QAC/E,KAAK,KAAK;QACV,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,wBAAwB;QAC5B,MAAM,SAAS;YACb,SAAS;gBACP,YAAY,uBAAuB,MAAM;gBACzC,aAAa,uBAAuB,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;gBACnF,iBAAiB,IAAI,IAAI,uBAAuB,GAAG,CAAC,CAAA,IAAK,GAAG,EAAE,aAAa,CAAC,CAAC,EAAE,EAAE,oBAAoB,EAAE,GAAG,IAAI;gBAC9G,mBAAmB,uBAAuB,MAAM,GAAG,IAC/C,uBAAuB,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE,KAAK,uBAAuB,MAAM,GACxG;YACN;YACA,mBAAmB;YACnB,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,MAAM,cAAc,KAAK,SAAS,CAAC,QAAQ,MAAM;QACjD,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAAmB;QAChE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,CAAC,eAAe,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;QAC/E,KAAK,KAAK;QACV,IAAI,eAAe,CAAC;IACtB;IAIA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAAS,YAAY;gCACxD,iBAAiB,kBAAkB,SAC/B,kEACA;gCACJ,gBAAgB;gCAChB,WAAW;4BACb;;;;;;sCAEF,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAAS,YAAY;gCACxD,iBAAiB,kBAAkB,SAC/B,kEACA;gCACJ,gBAAgB;gCAChB,WAAW;4BACb;;;;;;;;;;;;8BAKJ,8OAAC,qIAAA,CAAA,UAAe;oBAAC,MAAK;oBAAQ,OAAO;;;;;;;;;;;;IAG3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDACL,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;;;;;;0DAEF,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDAClD,SAAS,IAAM,mBAAmB,WAAW,MAAM,IAAI;gDACvD,QAAQ,IAAM,WAAW,IAAM,mBAAmB,QAAQ;gDAC1D,WAAU;gDACV,OAAO;oDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oDACzD,OAAO,kBAAkB,SAAS,YAAY;gDAChD;;;;;;4CAID,mBAAmB,kBAAkB,MAAM,GAAG,mBAC7C,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;gDAC3D;0DAEC,kBAAkB,GAAG,CAAC,CAAC,YAAY,sBAClC,8OAAC;wDAEC,SAAS,IAAM,sBAAsB;wDACrC,WAAU;wDACV,OAAO;4DACL,OAAO,kBAAkB,SAAS,YAAY;wDAChD;;0EAEA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB;;uDARI;;;;;;;;;;;;;;;;kDAgBf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,KAAK,MAAM,KAAK,WAAW,QAAQ;wDAAS,CAAC;gDACnG,WAAW,CAAC,qEAAqE,EAC/E,QAAQ,MAAM,KAAK,WACf,8CACA,8DACL,OAAO,CAAC;;kEAET,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG3C,8OAAC;gDACC,SAAS,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,KAAK,MAAM,KAAK,YAAY,QAAQ;wDAAU,CAAC;gDACrG,WAAW,CAAC,qEAAqE,EAC/E,QAAQ,MAAM,KAAK,YACf,2CACA,8DACL,OAAO,CAAC;;kEAET,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAGnD,8OAAC;gDACC,SAAS,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,KAAK,MAAM,KAAK,gBAAgB,QAAQ;wDAAc,CAAC;gDAC7G,WAAW,CAAC,qEAAqE,EAC/E,QAAQ,MAAM,KAAK,gBACf,oDACA,8DACL,OAAO,CAAC;;kEAET,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;;;;;;;;;;;;;0CAOpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAW,CAAC,yEAAyE,EACnF,cACI,iDACA,8DACL,OAAO,CAAC;;0DAET,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;oCAK3C,uBAAuB,MAAM,GAAG,mBAC/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAwB;kEAE5C,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;0DAGzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;wDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oDAC3D;;sEAEA,8OAAC;4DACC,SAAS;4DACT,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;;8EAEA,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,8OAAC;4DACC,SAAS;4DACT,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;;8EAEA,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,8OAAC;4DACC,WAAU;4DACV,OAAO;gEACL,iBAAiB,kBAAkB,SAAS,YAAY;4DAC1D;;;;;;sEAEF,8OAAC;4DACC,SAAS;4DACT,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;;8EAEA,8OAAC,kNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhD,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;oBAOtC,6BACC,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,YAAY;4BACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wBAC3D;kCAEA,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;4CAAiC,OAAO;gDACvD,OAAO,kBAAkB,SAAS,YAAY;4CAChD;sDAAG;;;;;;sDAGH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,OAAO,QAAQ,SAAS,CAAC,IAAI;oDAC7B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEACnC,GAAG,IAAI;gEACP,WAAW;oEAAE,GAAG,KAAK,SAAS;oEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gEAAC;4DACvD,CAAC;oDACD,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;wDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wDACzD,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;;8DAEF,8OAAC;oDACC,MAAK;oDACL,OAAO,QAAQ,SAAS,CAAC,EAAE;oDAC3B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEACnC,GAAG,IAAI;gEACP,WAAW;oEAAE,GAAG,KAAK,SAAS;oEAAE,IAAI,EAAE,MAAM,CAAC,KAAK;gEAAC;4DACrD,CAAC;oDACD,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;wDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wDACzD,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;;;;;;;;;;;;;;8CAMN,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;4CAAiC,OAAO;gDACvD,OAAO,kBAAkB,SAAS,YAAY;4CAChD;sDAAG;;;;;;sDAGH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,QAAQ,WAAW,CAAC,GAAG;oDAC9B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEACnC,GAAG,IAAI;gEACP,aAAa;oEAAE,GAAG,KAAK,WAAW;oEAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK;gEAAE;4DACvE,CAAC;oDACD,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;wDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wDACzD,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;;8DAEF,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO,QAAQ,WAAW,CAAC,GAAG;oDAC9B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEACnC,GAAG,IAAI;gEACP,aAAa;oEAAE,GAAG,KAAK,WAAW;oEAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK;gEAAM;4DAC3E,CAAC;oDACD,WAAU;oDACV,OAAO;wDACL,iBAAiB,kBAAkB,SAAS,YAAY;wDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;wDACzD,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;;;;;;;;;;;;;;8CAMN,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;4CAAiC,OAAO;gDACvD,OAAO,kBAAkB,SAAS,YAAY;4CAChD;sDAAG;;;;;;sDAGH,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,QAAQ;4CACvB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC1E,WAAU;4CACV,OAAO;gDACL,iBAAiB,kBAAkB,SAAS,YAAY;gDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;gDACzD,OAAO,kBAAkB,SAAS,YAAY;4CAChD;;;;;;;;;;;;8CAKJ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,WAAW;gDACxB,QAAQ;gDACR,WAAW;oDAAE,MAAM;oDAAI,IAAI;gDAAG;gDAC9B,aAAa;oDAAE,KAAK;oDAAG,KAAK;gDAAM;gDAClC,QAAQ;gDACR,UAAU;4CACZ;wCACA,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;;;;;;;;;;;;;;;;;kCASvD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;wCAAsB,OAAO;4CAC3C,OAAO,kBAAkB,SAAS,YAAY;wCAChD;kDACG,aAAa,aACV,GAAG,kBAAkB,MAAM,CAAC,SAAS,EAAE,kBAAkB,MAAM,KAAK,IAAI,MAAM,GAAG,MAAM,EAAE,uBAAuB,MAAM,CAAC,KAAK,EAAE,uBAAuB,MAAM,KAAK,IAAI,MAAM,IAAI,GAC9K,GAAG,uBAAuB,MAAM,CAAC,YAAY,EAAE,uBAAuB,MAAM,KAAK,IAAI,MAAM,GAAG,MAAM,CAAC;;;;;;oCAK1G,uBAAuB,MAAM,GAAG,KAAK,aAAa,4BACjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;gDACV,OAAO,cAAc,IAAI,KAAK,uBAAuB,MAAM,GAAG,iBAAiB;0DAE9E,cAAc,IAAI,KAAK,uBAAuB,MAAM,iBACnD,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;2DACrB,cAAc,IAAI,GAAG,kBACvB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;yEAEjB,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;4CAIrB,cAAc,IAAI,GAAG,mBACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DACb,cAAc,IAAI;4DAAC;;;;;;;kEAEtB,8OAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;0CAUtD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;gDAAsB,OAAO;oDAC3C,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DAAG;;;;;;0DAGH,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,WAAU;gDACV,OAAO;oDACL,iBAAiB,kBAAkB,SAAS,YAAY;oDACxD,QAAQ,kBAAkB,SAAS,sBAAsB;oDACzD,OAAO,kBAAkB,SAAS,YAAY;gDAChD;;kEAEA,8OAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,8OAAC;wDAAO,OAAM;kEAAgB;;;;;;kEAC9B,8OAAC;wDAAO,OAAM;kEAAe;;;;;;kEAC7B,8OAAC;wDAAO,OAAM;kEAAe;;;;;;kEAC7B,8OAAC;wDAAO,OAAM;kEAAa;;;;;;;;;;;;0DAE7B,8OAAC;gDACC,SAAS,IAAM,iBAAiB,kBAAkB,QAAQ,SAAS;gDACnE,WAAU;gDACV,OAAO,CAAC,KAAK,EAAE,kBAAkB,QAAQ,eAAe,aAAa;0DAEpE,kBAAkB,sBACjB,8OAAC,8NAAA,CAAA,UAAO;oDAAC,WAAU;oDAAU,OAAO;wDAClC,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;yEAEA,8OAAC,iOAAA,CAAA,WAAQ;oDAAC,WAAU;oDAAU,OAAO;wDACnC,OAAO,kBAAkB,SAAS,YAAY;oDAChD;;;;;;;;;;;;;;;;;kDAMN,8OAAC;wCAAI,WAAU;wCAAyB,OAAO;4CAC7C,QAAQ,kBAAkB,SAAS,sBAAsB;wCAC3D;;0DACE,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,oCAAoC,EAC9C,aAAa,aACT,gCACA,4CACJ;gDACF,OAAM;;kEAEN,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG3C,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,oCAAoC,EAC9C,aAAa,SACT,gCACA,4CACJ;gDACF,OAAM;;kEAEN,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG1C,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,oCAAoC,EAC9C,aAAa,SACT,gCACA,4CACJ;gDACF,OAAM;;kEAEN,8OAAC,yMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlD,8OAAC;gBAAI,WAAU;;oBACZ,aAAa,aACZ,wBAAwB;oBACxB,kBAAkB,MAAM,GAAG,IACzB,kBAAkB,GAAG,CAAC,CAAC,yBACrB,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAAS,YAAY;gCACxD,QAAQ,kBAAkB,SAAS,sBAAsB;4BAC3D;;8CAGA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,kBAAkB,SAAS,YAAY;wCACxD,aAAa,kBAAkB,SAAS,YAAY;oCACtD;;sDAEA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oIAAA,CAAA,UAAc;4DAEb,cAAc,SAAS,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;4DACjD,oBAAoB,SAAS,YAAY,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC;4DACnE,mBAAmB,iBAAiB,GAAG,CAAC,SAAS,YAAY,GAAG;4DAChE,MAAK;4DACL,gBAAgB;4DAChB,aAAa,IAAM,kBACjB,SAAS,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EACnC,SAAS,YAAY,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC;2DAR5C,GAAG,SAAS,YAAY,CAAC,CAAC,EAAE,kBAAkB;;;;;sEAWrD,8OAAC;;8EACC,8OAAC;oEACC,WAAU;oEACV,OAAO;wEACL,OAAO,kBAAkB,SAAS,YAAY;oEAChD;8EAEC,SAAS,YAAY;;;;;;8EAExB,8OAAC;oEACC,WAAU;oEACV,OAAO;wEACL,OAAO,kBAAkB,SAAS,YAAY;oEAChD;;wEAEC,SAAS,UAAU;wEAAC;wEAAa,SAAS,UAAU,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;8DAI1E,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;;4EAAsC;4EAC/C,SAAS,WAAW,CAAC,OAAO,CAAC;;;;;;;kFAEjC,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;4DAGtC,SAAS,aAAa,GAAG,mBACxB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;;4EAAuC;4EAC/C,SAAS,aAAa,CAAC,OAAO,CAAC;;;;;;;kFAEpC,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAIzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAW,CAAC,mBAAmB,EAChC,SAAS,gBAAgB,IAAI,IAAI,mBAAmB,gBACpD;;4EAAE;4EACA,SAAS,gBAAgB,CAAC,OAAO,CAAC;;;;;;;kFAEtC,8OAAC;wEAAE,WAAU;kFACV,SAAS,gBAAgB,IAAI,IAAI,eAAe;;;;;;;;;;;;4DAIpD,SAAS,gBAAgB,GAAG,mBAC3B,8OAAC;gEACC,SAAS;oEACP,MAAM,YAAY,SAAS,YAAY,CAAC,KAAK,CAAC;oEAC9C,MAAM,YAAY,SAAS,CAAC,EAAE,IAAI;oEAClC,MAAM,WAAW,UAAU,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ;oEACjD,kBAAkB,WAAW;gEAC/B;gEACA,WAAU;;kFAEV,8OAAC,kNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDASxD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;;gEACD;gEACU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,UAAU,GAAG;;;;;;;;;;;;;8DAGnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,kBAAkB,SAAS,YAAY;4DAChD;;gEACD;gEACU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,UAAU,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;8CAOvD,8OAAC;oCAAI,WAAU;oCAAW,OAAO;wCAC/B,aAAa,kBAAkB,SAAS,YAAY;oCACtD;8CACG,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,qBACnB,8OAAC;4CAAkB,WAAU;sDAC3B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;;;;;;kFAErB,8OAAC;wEACC,WAAU;wEACV,OAAO;4EACL,OAAO,kBAAkB,SAAS,YAAY;wEAChD;kFAEC,KAAK,YAAY;;;;;;;;;;;;0EAItB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;wEAAkC,OAAO;4EACtD,OAAO,kBAAkB,SAAS,YAAY;wEAChD;;0FACE,8OAAC;gFAAK,WAAU;;kGACd,8OAAC,8MAAA,CAAA,aAAU;wFAAC,WAAU;;;;;;oFAAY;oFAC5B,KAAK,QAAQ;;;;;;;0FAErB,8OAAC;gFAAK,WAAU;;kGACd,8OAAC,kNAAA,CAAA,aAAU;wFAAC,WAAU;;;;;;oFAAY;oFAC1B,KAAK,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;kFAGvC,8OAAC;wEAAI,WAAU;wEAAkC,OAAO;4EACtD,OAAO,kBAAkB,SAAS,YAAY;wEAChD;;0FACE,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,8OAAC;0FAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG;;;;;;0FACxC,8OAAC;gFAAK,WAAU;0FAAO;;;;;;0FACvB,8OAAC;gFAAK,WAAW,CAAC,2CAA2C,EAC3D,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,QAAQ,IAAI,KAAK,KAAK,SAAS,KAAK,KACrD,4BACA,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,QAAQ,IAAI,KAAK,KAAK,SAAS,KAAK,IACvD,kCACA,+BACN;;oFACC,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,QAAQ,IAAI,KAAK,KAAK,SAAS;oFAAG;;;;;;;;;;;;;;;;;;;;;;;;;kEAMhE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAU;;wEAAiC;wEAC1C,KAAK,YAAY,CAAC,OAAO,CAAC;;;;;;;;;;;;0EAIhC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,SAAS,IAAM,WAAW;wEAC1B,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC;wEACC,SAAS,IAAM,aAAa,KAAK,EAAE;wEACnC,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CArElB,KAAK,EAAE;;;;;;;;;;;2BA/HhB,SAAS,YAAY;;;;kDA+M9B,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,0BAA0B;4BACtE,aAAa,kBAAkB,SAAS,YAAY;wBACtD;;0CAEA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,2BAA2B;oCACvE,QAAQ,kBAAkB,SAAS,qCAAqC;gCAC1E;0CAEA,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCACJ,WAAU;oCACV,OAAO;wCACL,OAAO,kBAAkB,SAAS,YAAY;oCAChD;;;;;;;;;;;0CAGJ,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,kBAAkB,SAAS,YAAY;gCAChD;0CACD;;;;;;0CAGD,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,kBAAkB,SAAS,YAAY;gCAChD;0CAEC,cAAc,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,IAAK,KAAK,MAAM,SACvD,wFACA;;;;;;4BAEL,CAAC,cAAc,CAAC,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,IAAK,KAAK,MAAM,wBAC3D,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;+BAOzC,sBAAsB;kCACtB,8OAAC;wBAAI,WAAW,aAAa,SACzB,yDACA;kCAED,uBAAuB,GAAG,CAAC,CAAC,qBAC3B,8OAAC;gCAEC,WAAW,CAAC,gGAAgG,EAC1G,cAAc,GAAG,CAAC,KAAK,EAAE,IACrB,wCACA,qBACL,CAAC,EAAE,aAAa,SAAS,sBAAsB,IAAI;gCACpD,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,YAAY;oCACxD,QAAQ,kBAAkB,SAAS,sBAAsB;gCAC3D;;kDAGA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;4CACvC,WAAW,CAAC,2CAA2C,EACrD,cAAc,GAAG,CAAC,KAAK,EAAE,IACrB,4BACA,2CACL,UAAU,CAAC;sDAEX,cAAc,GAAG,CAAC,KAAK,EAAE,kBACxB,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAMxB,8OAAC;wCAAI,WAAW,GAAG,aAAa,SAAS,QAAQ,cAAc;kDAC7D,cAAA,8OAAC;4CAAI,WAAW,GAAG,aAAa,SAAS,sCAAsC,IAAI;;8DACjF,8OAAC;oDAAI,WAAW,GAAG,aAAa,SAAS,WAAW,IAAI;;sEAEtD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oIAAA,CAAA,UAAc;oEAEb,cAAc,KAAK,aAAa;oEAChC,oBAAoB,KAAK,oBAAoB;oEAC7C,mBAAmB,iBAAiB,GAAG,CAAC,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,EAAE,GAAG;oEAC/F,MAAK;oEACL,gBAAgB;mEALX,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,oBAAoB,CAAC,CAAC,EAAE,kBAAkB;;;;;8EAO/E,8OAAC;;sFACC,8OAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;;gFAEC,KAAK,aAAa;gFAAC;gFAAE,KAAK,oBAAoB;;;;;;;sFAEjD,8OAAC;4EACC,WAAU;4EACV,OAAO;gFACL,OAAO,kBAAkB,SAAS,YAAY;4EAChD;sFAEC,KAAK,YAAY;;;;;;;;;;;;;;;;;;sEAMxB,8OAAC;4DAAI,WAAW,GAAG,aAAa,SAAS,4BAA4B,aAAa;;8EAChF,8OAAC;oEAAI,WAAU;oEAAkC,OAAO;wEACtD,OAAO,kBAAkB,SAAS,YAAY;oEAChD;;sFACE,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,8MAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;gFAAY;gFAC5B,KAAK,QAAQ;;;;;;;sFAErB,8OAAC;4EAAK,WAAU;;8FACd,8OAAC,kNAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;gFAAY;gFAChC,KAAK,aAAa,CAAC,OAAO,CAAC;;;;;;;;;;;;;8EAIjC,8OAAC;oEAAI,WAAU;oEAAkC,OAAO;wEACtD,OAAO,kBAAkB,SAAS,YAAY;oEAChD;;sFACE,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC;sFAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG;;;;;;;;;;;;8EAG1C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;;gFAAkC;gFAC9C,KAAK,YAAY,CAAC,OAAO,CAAC;;;;;;;sFAE9B,8OAAC;4EAAK,WAAW,CAAC,2CAA2C,EAC3D,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,QAAQ,IAAI,KAAK,KAAK,SAAS,KAAK,KACrD,4BACA,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,QAAQ,IAAI,KAAK,KAAK,SAAS,KAAK,IACvD,kCACA,+BACN;;gFACC,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,QAAQ,IAAI,KAAK,KAAK,SAAS;gFAAG;;;;;;;;;;;;;;;;;;;;;;;;;8DAOhE,8OAAC;oDAAI,WAAW,GAAG,aAAa,SAAS,oBAAoB,mBAAmB;;sEAC9E,8OAAC;4DACC,SAAS,IAAM,WAAW;4DAC1B,WAAU;4DACV,OAAM;;8EAEN,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,aAAa,SAAS,SAAS;;;;;;;sEAElC,8OAAC;4DACC,SAAS,IAAM,aAAa,KAAK,EAAE;4DACnC,WAAU;4DACV,OAAM;;8EAEN,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,aAAa,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;+BAtHrC,KAAK,EAAE;;;;;;;;;;oBAiInB,CAAC,aAAa,UAAU,aAAa,MAAM,KAAK,uBAAuB,MAAM,KAAK,KAAK,CAAC,yBACvF,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,kBAAkB,SAAS,0BAA0B;4BACtE,aAAa,kBAAkB,SAAS,YAAY;wBACtD;;0CAEA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB,kBAAkB,SAAS,2BAA2B;oCACvE,QAAQ,kBAAkB,SAAS,qCAAqC;gCAC1E;0CAEA,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oCACP,WAAU;oCACV,OAAO;wCACL,OAAO,kBAAkB,SAAS,YAAY;oCAChD;;;;;;;;;;;0CAGJ,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,kBAAkB,SAAS,YAAY;gCAChD;0CAEC,cAAc,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,IAAK,KAAK,MAAM,SAAS,0BAA0B;;;;;;0CAEhG,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,OAAO,kBAAkB,SAAS,YAAY;gCAChD;0CAEC,cAAc,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,IAAK,KAAK,MAAM,SACvD,gFACA;;;;;;4BAEL,CAAC,cAAc,CAAC,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,IAAK,KAAK,MAAM,wBAC3D,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAS3C,8OAAC,+HAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS;gBACT,MAAM;;;;;;0BAIR,8OAAC,kIAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,SAAS;gBACT,cAAc,4BAA4B,QAAQ;gBAClD,oBAAoB,4BAA4B,cAAc;gBAC9D,gBAAgB,4BAA4B,WAAW;gBACvD,SAAS;gBACT,mBACE,6BACI,iBAAiB,GAAG,CAAC,GAAG,2BAA2B,IAAI,CAAC,CAAC,EAAE,2BAA2B,UAAU,EAAE,GAAG,sBACrG;;;;;;0BAKR,8OAAC,0IAAA,CAAA,UAAoB;gBACnB,QAAQ;gBACR,SAAS;gBACT,UAAU,4BAA4B,YAAY;gBAClD,cAAc,4BAA4B,QAAQ;gBAClD,oBAAoB,4BAA4B,cAAc;gBAC9D,kBAAkB;;;;;;;;;;;;AAI1B", "debugId": null}}]}