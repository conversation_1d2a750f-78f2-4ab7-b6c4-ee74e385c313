(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/env.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Environment variables validation and configuration
__turbopack_context__.s({
    "CLOUDINARY_API_KEY": (()=>CLOUDINARY_API_KEY),
    "CLOUDINARY_API_SECRET": (()=>CLOUDINARY_API_SECRET),
    "DEBUG": (()=>DEBUG),
    "GEMINI_API_KEY": (()=>GEMINI_API_KEY),
    "NEXTAUTH_SECRET": (()=>NEXTAUTH_SECRET),
    "NEXTAUTH_URL": (()=>NEXTAUTH_URL),
    "NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME": (()=>NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME),
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": (()=>NEXT_PUBLIC_SUPABASE_ANON_KEY),
    "NEXT_PUBLIC_SUPABASE_URL": (()=>NEXT_PUBLIC_SUPABASE_URL),
    "NODE_ENV": (()=>NODE_ENV),
    "SUPABASE_SERVICE_ROLE_KEY": (()=>SUPABASE_SERVICE_ROLE_KEY),
    "checkDevelopmentEnvVars": (()=>checkDevelopmentEnvVars),
    "checkProductionEnvVars": (()=>checkProductionEnvVars),
    "checkRequiredEnvVars": (()=>checkRequiredEnvVars),
    "config": (()=>config),
    "env": (()=>env),
    "initializeEnv": (()=>initializeEnv)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v3/external.js [app-client] (ecmascript) <export * as z>");
;
// Define the schema for environment variables
const envSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    // Node environment
    NODE_ENV: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        'development',
        'production',
        'test'
    ]).default('development'),
    // Supabase configuration
    NEXT_PUBLIC_SUPABASE_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    NEXT_PUBLIC_SUPABASE_ANON_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    SUPABASE_SERVICE_ROLE_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    // Cloudinary configuration
    NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    CLOUDINARY_API_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    CLOUDINARY_API_SECRET: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    // Google Gemini AI configuration
    GEMINI_API_KEY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    // Authentication (optional for build time)
    NEXTAUTH_SECRET: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    NEXTAUTH_URL: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    // Optional configuration
    DEBUG: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().transform((val)=>val === 'true').default('false')
});
// Parse and validate environment variables
function validateEnv() {
    try {
        return envSchema.parse(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env);
    } catch (error) {
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].ZodError) {
            const missingVars = error.errors.map((err)=>`${err.path.join('.')}: ${err.message}`);
            throw new Error(`❌ Invalid environment variables:\n${missingVars.join('\n')}\n\n` + `Please check your .env.local file and ensure all required variables are set.\n` + `See .env.example for reference.`);
        }
        throw error;
    }
}
const env = validateEnv();
const config = {
    isDevelopment: env.NODE_ENV === 'development',
    isProduction: env.NODE_ENV === 'production',
    isTest: env.NODE_ENV === 'test',
    // Database
    database: {
        url: env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
        anonKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key',
        serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY
    },
    // File storage
    cloudinary: {
        cloudName: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'placeholder',
        apiKey: env.CLOUDINARY_API_KEY,
        apiSecret: env.CLOUDINARY_API_SECRET
    },
    // AI configuration
    ai: {
        geminiApiKey: env.GEMINI_API_KEY
    },
    // Authentication
    auth: {
        secret: env.NEXTAUTH_SECRET,
        url: env.NEXTAUTH_URL
    },
    // Debug mode
    debug: env.DEBUG
};
function checkRequiredEnvVars() {
    const requiredVars = [
        'NEXT_PUBLIC_SUPABASE_URL',
        'NEXT_PUBLIC_SUPABASE_ANON_KEY',
        'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME'
    ];
    const missingVars = requiredVars.filter((varName)=>!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env[varName]);
    if (missingVars.length > 0) {
        throw new Error(`❌ Missing required environment variables: ${missingVars.join(', ')}\n` + `Please check your .env.local file.`);
    }
}
function checkDevelopmentEnvVars() {
    if (config.isDevelopment) {
        const devVars = [
            'CLOUDINARY_API_KEY',
            'CLOUDINARY_API_SECRET',
            'NEXTAUTH_SECRET'
        ];
        const missingDevVars = devVars.filter((varName)=>!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env[varName]);
        if (missingDevVars.length > 0) {
            console.warn(`⚠️  Missing development environment variables: ${missingDevVars.join(', ')}\n` + `Some features may not work properly.`);
        }
    }
}
function checkProductionEnvVars() {
    if (config.isProduction) {
        const prodVars = [
            'SUPABASE_SERVICE_ROLE_KEY',
            'CLOUDINARY_API_KEY',
            'CLOUDINARY_API_SECRET',
            'NEXTAUTH_SECRET',
            'NEXTAUTH_URL'
        ];
        const missingProdVars = prodVars.filter((varName)=>!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env[varName]);
        if (missingProdVars.length > 0) {
            throw new Error(`❌ Missing production environment variables: ${missingProdVars.join(', ')}\n` + `These are required for production deployment.`);
        }
    }
}
function initializeEnv() {
    try {
        checkRequiredEnvVars();
        checkDevelopmentEnvVars();
        checkProductionEnvVars();
        if (config.debug) {
            console.warn('✅ Environment variables validated successfully');
            console.warn('📊 Configuration:', {
                environment: env.NODE_ENV,
                database: !!config.database.url,
                cloudinary: !!config.cloudinary.cloudName,
                auth: !!config.auth.secret
            });
        }
    } catch (error) {
        console.error(error);
        if (config.isProduction) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].exit(1);
        }
    }
}
const { NODE_ENV, NEXT_PUBLIC_SUPABASE_URL, NEXT_PUBLIC_SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY, NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, CLOUDINARY_API_SECRET, NEXTAUTH_SECRET, NEXTAUTH_URL, DEBUG, GEMINI_API_KEY } = env;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/supabase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PRODUCT_CATEGORIES": (()=>PRODUCT_CATEGORIES),
    "supabase": (()=>supabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$env$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/env.ts [app-client] (ecmascript)");
;
;
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$env$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["config"].database.url, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$env$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["config"].database.anonKey, {
    auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
    },
    db: {
        schema: 'public'
    },
    global: {
        headers: {
            'X-Client-Info': 'revantad-store@1.0.0'
        }
    }
});
const PRODUCT_CATEGORIES = [
    'Snacks',
    'Canned Goods',
    'Beverages',
    'Personal Care',
    'Household Items',
    'Condiments',
    'Rice & Grains',
    'Instant Foods',
    'Dairy Products',
    'Others'
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/admin/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AdminPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AdminHeader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AdminHeader$3e$__ = __turbopack_context__.i("[project]/src/components/AdminHeader.tsx [app-client] (ecmascript) <export default as AdminHeader>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Sidebar$3e$__ = __turbopack_context__.i("[project]/src/components/Sidebar.tsx [app-client] (ecmascript) <export default as Sidebar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ProductsSection$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ProductsSection$3e$__ = __turbopack_context__.i("[project]/src/components/ProductsSection.tsx [app-client] (ecmascript) <export default as ProductsSection>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$DebtsSection$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DebtsSection$3e$__ = __turbopack_context__.i("[project]/src/components/DebtsSection.tsx [app-client] (ecmascript) <export default as DebtsSection>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$DashboardStats$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DashboardStats$3e$__ = __turbopack_context__.i("[project]/src/components/DashboardStats.tsx [app-client] (ecmascript) <export default as DashboardStats>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$FamilyGallery$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FamilyGallery$3e$__ = __turbopack_context__.i("[project]/src/components/FamilyGallery.tsx [app-client] (ecmascript) <export default as FamilyGallery>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$APIGraphing$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__APIGraphing$3e$__ = __turbopack_context__.i("[project]/src/components/APIGraphing.tsx [app-client] (ecmascript) <export default as APIGraphing>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$History$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__History$3e$__ = __turbopack_context__.i("[project]/src/components/History.tsx [app-client] (ecmascript) <export default as History>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Calendar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/src/components/Calendar.tsx [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Settings$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/src/components/Settings.tsx [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ProtectedRoute$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ProtectedRoute$3e$__ = __turbopack_context__.i("[project]/src/components/ProtectedRoute.tsx [app-client] (ecmascript) <export default as ProtectedRoute>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AIAssistant$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AIAssistant$3e$__ = __turbopack_context__.i("[project]/src/components/AIAssistant.tsx [app-client] (ecmascript) <export default as AIAssistant>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AISupport$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AISupport$3e$__ = __turbopack_context__.i("[project]/src/components/AISupport.tsx [app-client] (ecmascript) <export default as AISupport>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function AdminPage() {
    _s();
    const [activeSection, setActiveSection] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('dashboard');
    const { resolvedTheme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    const [stats, setStats] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        totalProducts: 0,
        totalDebts: 0,
        totalDebtAmount: 0,
        lowStockItems: 0,
        recentProducts: [],
        recentDebts: []
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AdminPage.useEffect": ()=>{
            fetchStats();
        }
    }["AdminPage.useEffect"], []);
    const fetchStats = async ()=>{
        try {
            // Fetch products
            const productsRes = await fetch('/api/products');
            const productsData = await productsRes.json();
            const products = productsData.products || [];
            // Fetch debts
            const debtsRes = await fetch('/api/debts');
            const debtsData = await debtsRes.json();
            const debts = debtsData.debts || [];
            // Calculate stats
            const totalDebtAmount = debts.reduce((sum, debt)=>sum + debt.total_amount, 0);
            const lowStockProducts = products.filter((product)=>product.stock_quantity < 10).length;
            setStats({
                totalProducts: products.length,
                totalDebts: debts.length,
                totalDebtAmount,
                lowStockItems: lowStockProducts,
                recentProducts: products.slice(0, 5),
                recentDebts: debts.slice(0, 5)
            });
        } catch (error) {
            console.error('Error fetching stats:', error);
        }
    };
    const renderContent = ()=>{
        console.warn('🎯 Rendering content for section:', activeSection);
        switch(activeSection){
            case 'products':
                console.warn('📦 Rendering ProductsSection');
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ProductsSection$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ProductsSection$3e$__["ProductsSection"], {
                    onStatsUpdate: fetchStats
                }, void 0, false, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 74,
                    columnNumber: 16
                }, this);
            case 'debts':
                console.warn('💳 Rendering DebtsSection');
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$DebtsSection$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DebtsSection$3e$__["DebtsSection"], {
                    onStatsUpdate: fetchStats
                }, void 0, false, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 77,
                    columnNumber: 16
                }, this);
            case 'family-gallery':
                console.warn('🖼️ Rendering FamilyGallery');
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$FamilyGallery$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FamilyGallery$3e$__["FamilyGallery"], {}, void 0, false, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 80,
                    columnNumber: 16
                }, this);
            case 'api-graphing':
                console.warn('📊 Rendering APIGraphing');
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$APIGraphing$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__APIGraphing$3e$__["APIGraphing"], {
                    stats: stats
                }, void 0, false, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 83,
                    columnNumber: 16
                }, this);
            case 'history':
                console.warn('📜 Rendering History');
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$History$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__History$3e$__["History"], {}, void 0, false, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 86,
                    columnNumber: 16
                }, this);
            case 'calendar':
                console.warn('📅 Rendering Calendar');
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Calendar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {}, void 0, false, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 89,
                    columnNumber: 16
                }, this);
            case 'settings':
                console.warn('⚙️ Rendering Settings');
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Settings$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {}, void 0, false, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 92,
                    columnNumber: 16
                }, this);
            case 'ai-support':
                console.warn('🤖 Rendering AISupport');
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AISupport$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AISupport$3e$__["AISupport"], {}, void 0, false, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 95,
                    columnNumber: 16
                }, this);
            default:
                console.warn('🏠 Rendering DashboardStats (default)');
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$DashboardStats$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DashboardStats$3e$__["DashboardStats"], {
                    stats: stats,
                    onSectionChange: setActiveSection
                }, void 0, false, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 98,
                    columnNumber: 16
                }, this);
        }
    };
    const getPageTitle = ()=>{
        switch(activeSection){
            case 'dashboard':
                return 'Dashboard';
            case 'products':
                return 'Product Lists';
            case 'debts':
                return 'Customer Debt Management';
            case 'family-gallery':
                return 'Family Gallery';
            case 'api-graphing':
                return 'API Graphing & Visuals';
            case 'history':
                return 'History';
            case 'calendar':
                return 'Calendar';
            case 'settings':
                return 'Settings';
            case 'ai-support':
                return 'AI Support';
            default:
                return 'Dashboard';
        }
    };
    const getPageDescription = ()=>{
        switch(activeSection){
            case 'dashboard':
                return 'Overview of your Revantad Store';
            case 'products':
                return 'Manage your product lists with CRUD operations';
            case 'debts':
                return 'Track customer debt and payments';
            case 'family-gallery':
                return 'Manage family photos and memories';
            case 'api-graphing':
                return 'Visual analytics and business insights';
            case 'history':
                return 'View transaction and activity history';
            case 'calendar':
                return 'Manage events and schedules';
            case 'settings':
                return 'Configure your store settings';
            case 'ai-support':
                return 'Get intelligent assistance for your store management';
            default:
                return 'Overview of your Revantad Store';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ProtectedRoute$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ProtectedRoute$3e$__["ProtectedRoute"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300",
            style: {
                backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#f9fafb'
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AdminHeader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AdminHeader$3e$__["AdminHeader"], {
                    activeSection: activeSection,
                    setActiveSection: setActiveSection
                }, void 0, false, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 161,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex pt-16",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Sidebar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Sidebar$3e$__["Sidebar"], {
                            activeSection: activeSection,
                            setActiveSection: setActiveSection
                        }, void 0, false, {
                            fileName: "[project]/src/app/admin/page.tsx",
                            lineNumber: 168,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                            className: "flex-1 transition-colors duration-300 main-content-scroll",
                            style: {
                                backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#ffffff',
                                height: 'calc(100vh - 4rem)',
                                overflowY: 'auto',
                                overflowX: 'hidden'
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-8",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-8",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "text-3xl font-bold transition-colors duration-300",
                                                style: {
                                                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937'
                                                },
                                                children: getPageTitle()
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/page.tsx",
                                                lineNumber: 182,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "mt-2 transition-colors duration-300",
                                                style: {
                                                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                                                },
                                                children: getPageDescription()
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/page.tsx",
                                                lineNumber: 190,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/admin/page.tsx",
                                        lineNumber: 181,
                                        columnNumber: 15
                                    }, this),
                                    renderContent()
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/admin/page.tsx",
                                lineNumber: 180,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/admin/page.tsx",
                            lineNumber: 171,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 166,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AIAssistant$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AIAssistant$3e$__["AIAssistant"], {
                    context: activeSection
                }, void 0, false, {
                    fileName: "[project]/src/app/admin/page.tsx",
                    lineNumber: 205,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/admin/page.tsx",
            lineNumber: 154,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/admin/page.tsx",
        lineNumber: 153,
        columnNumber: 5
    }, this);
}
_s(AdminPage, "v1xYa5iC6IXkSrhdzCKxkSVkAV8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"]
    ];
});
_c = AdminPage;
var _c;
__turbopack_context__.k.register(_c, "AdminPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_c38397a2._.js.map