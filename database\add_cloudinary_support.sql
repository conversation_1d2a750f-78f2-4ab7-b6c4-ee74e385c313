-- Add Cloudinary support to existing customers table
-- Run this in Supabase SQL Editor to add Cloudinary public_id support

-- Add profile_picture_public_id column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' 
        AND column_name = 'profile_picture_public_id'
    ) THEN
        ALTER TABLE customers ADD COLUMN profile_picture_public_id TEXT;
    END IF;
END $$;

-- Verify the column was added
SELECT 'Cloudinary support added successfully!' as status;
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'customers' 
AND column_name IN ('profile_picture_url', 'profile_picture_public_id');
