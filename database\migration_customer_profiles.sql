-- Safe Migration Script for Customer Profile Pictures
-- Run this in Supabase SQL Editor to add customer profile functionality

-- Create customers table for profile management (only if it doesn't exist)
CREATE TABLE IF NOT EXISTS customers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    profile_picture_url TEXT,
    phone_number VARCHAR(20),
    address TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(customer_name, customer_family_name)
);

-- Create customer_payments table (only if it doesn't exist)
CREATE TABLE IF NOT EXISTS customer_payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    payment_amount DECIMAL(10,2) NOT NULL CHECK (payment_amount > 0),
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50) DEFAULT 'Cash',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create customer_balance view for real-time balance calculation
CREATE OR REPLACE VIEW customer_balances AS
SELECT 
    customer_name,
    customer_family_name,
    COALESCE(total_debt, 0) as total_debt,
    COALESCE(total_payments, 0) as total_payments,
    COALESCE(total_debt, 0) - COALESCE(total_payments, 0) as remaining_balance,
    last_debt_date,
    last_payment_date
FROM (
    SELECT 
        customer_name,
        customer_family_name,
        SUM(total_amount) as total_debt,
        MAX(debt_date) as last_debt_date
    FROM customer_debts 
    GROUP BY customer_name, customer_family_name
) debts
FULL OUTER JOIN (
    SELECT 
        customer_name,
        customer_family_name,
        SUM(payment_amount) as total_payments,
        MAX(payment_date) as last_payment_date
    FROM customer_payments 
    GROUP BY customer_name, customer_family_name
) payments USING (customer_name, customer_family_name);

-- Create new indexes (only if they don't exist)
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_payments_customer ON customer_payments(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_payments_date ON customer_payments(payment_date);

-- Create triggers for new tables (with safe creation)
DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
CREATE TRIGGER update_customers_updated_at 
    BEFORE UPDATE ON customers 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_payments_updated_at ON customer_payments;
CREATE TRIGGER update_customer_payments_updated_at 
    BEFORE UPDATE ON customer_payments 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample customer profiles (only if they don't exist)
INSERT INTO customers (customer_name, customer_family_name, phone_number, notes)
SELECT 'Juan', 'Dela Cruz', '09123456789', 'Regular customer - prefers instant foods'
WHERE NOT EXISTS (
    SELECT 1 FROM customers WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz'
);

INSERT INTO customers (customer_name, customer_family_name, phone_number, notes)
SELECT 'Maria', 'Santos', '09234567890', 'Frequent buyer of beverages'
WHERE NOT EXISTS (
    SELECT 1 FROM customers WHERE customer_name = 'Maria' AND customer_family_name = 'Santos'
);

INSERT INTO customers (customer_name, customer_family_name, phone_number, notes)
SELECT 'Pedro', 'Garcia', '09345678901', 'Prefers canned goods'
WHERE NOT EXISTS (
    SELECT 1 FROM customers WHERE customer_name = 'Pedro' AND customer_family_name = 'Garcia'
);

INSERT INTO customers (customer_name, customer_family_name, phone_number, notes)
SELECT 'Ana', 'Reyes', '09456789012', 'Coffee lover - regular customer'
WHERE NOT EXISTS (
    SELECT 1 FROM customers WHERE customer_name = 'Ana' AND customer_family_name = 'Reyes'
);

INSERT INTO customers (customer_name, customer_family_name, phone_number, notes)
SELECT 'Jose', 'Cruz', '09567890123', 'Bulk rice buyer'
WHERE NOT EXISTS (
    SELECT 1 FROM customers WHERE customer_name = 'Jose' AND customer_family_name = 'Cruz'
);

-- Insert sample payment records (only if they don't exist)
INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, payment_method, notes)
SELECT 'Juan', 'Dela Cruz', 15.00, '2024-01-20', 'Cash', 'Partial payment for pancit canton'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments 
    WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz' 
    AND payment_date = '2024-01-20'
);

INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, payment_method, notes)
SELECT 'Maria', 'Santos', 25.00, '2024-01-21', 'GCash', 'Full payment for Coca-Cola'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments 
    WHERE customer_name = 'Maria' AND customer_family_name = 'Santos' 
    AND payment_date = '2024-01-21'
);

-- Verify the setup
SELECT 'Migration completed successfully!' as status;
SELECT 'Tables created:' as info;
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('customers', 'customer_payments', 'customer_debts', 'products');

SELECT 'Views created:' as info;
SELECT table_name FROM information_schema.views 
WHERE table_schema = 'public' 
AND table_name = 'customer_balances';

SELECT 'Sample data verification:' as info;
SELECT 'Customers: ' || COUNT(*) as count FROM customers;
SELECT 'Customer Debts: ' || COUNT(*) as count FROM customer_debts;
SELECT 'Customer Payments: ' || COUNT(*) as count FROM customer_payments;
SELECT 'Customer Balances: ' || COUNT(*) as count FROM customer_balances;
