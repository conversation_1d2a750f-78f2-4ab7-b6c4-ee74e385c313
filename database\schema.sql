-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    image_url TEXT,
    net_weight VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    category VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create customer_debts table
CREATE TABLE IF NOT EXISTS customer_debts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL CHECK (product_price >= 0),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    total_amount DECIMAL(10,2) GENERATED ALWAYS AS (product_price * quantity) STORED,
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create customer_payments table
CREATE TABLE IF NOT EXISTS customer_payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    payment_amount DECIMAL(10,2) NOT NULL CHECK (payment_amount > 0),
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50) DEFAULT 'Cash',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create customer_balance view for real-time balance calculation
CREATE OR REPLACE VIEW customer_balances AS
SELECT
    customer_name,
    customer_family_name,
    COALESCE(total_debt, 0) as total_debt,
    COALESCE(total_payments, 0) as total_payments,
    COALESCE(total_debt, 0) - COALESCE(total_payments, 0) as remaining_balance,
    last_debt_date,
    last_payment_date
FROM (
    SELECT
        customer_name,
        customer_family_name,
        SUM(total_amount) as total_debt,
        MAX(debt_date) as last_debt_date
    FROM customer_debts
    GROUP BY customer_name, customer_family_name
) debts
FULL OUTER JOIN (
    SELECT
        customer_name,
        customer_family_name,
        SUM(payment_amount) as total_payments,
        MAX(payment_date) as last_payment_date
    FROM customer_payments
    GROUP BY customer_name, customer_family_name
) payments USING (customer_name, customer_family_name);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_customer_debts_customer ON customer_debts(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_debts_date ON customer_debts(debt_date);
CREATE INDEX IF NOT EXISTS idx_customer_debts_product ON customer_debts(product_name);
CREATE INDEX IF NOT EXISTS idx_customer_payments_customer ON customer_payments(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_payments_date ON customer_payments(payment_date);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_debts_updated_at
    BEFORE UPDATE ON customer_debts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_payments_updated_at
    BEFORE UPDATE ON customer_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing
INSERT INTO products (name, net_weight, price, stock_quantity, category) VALUES
('Lucky Me Pancit Canton', '60g', 15.00, 50, 'Instant Foods'),
('Coca-Cola', '330ml', 25.00, 30, 'Beverages'),
('Corned Beef', '150g', 45.00, 20, 'Canned Goods'),
('Shampoo Sachet', '12ml', 8.00, 100, 'Personal Care'),
('Instant Coffee', '25g', 12.00, 75, 'Beverages'),
('Rice', '1kg', 55.00, 25, 'Rice & Grains'),
('Soy Sauce', '200ml', 18.00, 40, 'Condiments'),
('Detergent Powder', '35g', 6.00, 80, 'Household Items');

-- Insert sample customer debt data
INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date) VALUES
('Juan', 'Dela Cruz', 'Lucky Me Pancit Canton', 15.00, 2, '2024-01-15'),
('Maria', 'Santos', 'Coca-Cola', 25.00, 1, '2024-01-16'),
('Pedro', 'Garcia', 'Corned Beef', 45.00, 1, '2024-01-17'),
('Ana', 'Reyes', 'Instant Coffee', 12.00, 3, '2024-01-18'),
('Jose', 'Cruz', 'Rice', 55.00, 1, '2024-01-19');
