'use client'

import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

import {
  AdminHeader,
  Sidebar,
  ProductsSection,
  DebtsSection,
  DashboardStats,
  FamilyGallery,
  APIGraphing,
  History,
  Calendar,
  Settings,
  ProtectedRoute,
  AIAssistant,
  AISupport
} from '@/components'
import DebugTest from '@/components/DebugTest'
import type { DashboardStats as DashboardStatsType } from '@/types'

export default function AdminPage() {
  const [activeSection, setActiveSection] = useState('dashboard')
  const { resolvedTheme } = useTheme()
  const [stats, setStats] = useState<DashboardStatsType>({
    totalProducts: 0,
    totalDebts: 0,
    totalDebtAmount: 0,
    lowStockItems: 0,
    recentProducts: [],
    recentDebts: []
  })

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      // Fetch products
      const productsRes = await fetch('/api/products')
      const productsData = await productsRes.json()
      const products = productsData.products || []

      // Fetch debts
      const debtsRes = await fetch('/api/debts')
      const debtsData = await debtsRes.json()
      const debts = debtsData.debts || []

      // Calculate stats
      const totalDebtAmount = debts.reduce((sum: number, debt: { total_amount: number }) => sum + debt.total_amount, 0)
      const lowStockProducts = products.filter((product: { stock_quantity: number }) => product.stock_quantity < 10).length

      setStats({
        totalProducts: products.length,
        totalDebts: debts.length,
        totalDebtAmount,
        lowStockItems: lowStockProducts,
        recentProducts: products.slice(0, 5),
        recentDebts: debts.slice(0, 5)
      })
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const renderContent = () => {
    console.log('🎯 Rendering content for section:', activeSection)

    switch (activeSection) {
      case 'products':
        console.log('📦 Rendering ProductsSection')
        return <ProductsSection onStatsUpdate={fetchStats} />
      case 'debts':
        console.log('💳 Rendering DebtsSection')
        return <DebtsSection onStatsUpdate={fetchStats} />
      case 'family-gallery':
        console.log('🖼️ Rendering FamilyGallery')
        return <FamilyGallery />
      case 'api-graphing':
        console.log('📊 Rendering APIGraphing')
        return <APIGraphing stats={stats} />
      case 'history':
        console.log('📜 Rendering History')
        return <History />
      case 'calendar':
        console.log('📅 Rendering Calendar')
        return <Calendar />
      case 'settings':
        console.log('⚙️ Rendering Settings')
        return <Settings />
      case 'ai-support':
        console.log('🤖 Rendering AISupport')
        return <AISupport />
      case 'debug':
        console.log('🧪 Rendering DebugTest')
        return <DebugTest />
      default:
        console.log('🏠 Rendering DashboardStats (default)')
        return <DashboardStats stats={stats} onSectionChange={setActiveSection} />
    }
  }

  const getPageTitle = () => {
    switch (activeSection) {
      case 'dashboard':
        return 'Dashboard'
      case 'products':
        return 'Product Lists'
      case 'debts':
        return 'Customer Debt Management'
      case 'family-gallery':
        return 'Family Gallery'
      case 'api-graphing':
        return 'API Graphing & Visuals'
      case 'history':
        return 'History'
      case 'calendar':
        return 'Calendar'
      case 'settings':
        return 'Settings'
      case 'ai-support':
        return 'AI Support'
      default:
        return 'Dashboard'
    }
  }

  const getPageDescription = () => {
    switch (activeSection) {
      case 'dashboard':
        return 'Overview of your Revantad Store'
      case 'products':
        return 'Manage your product lists with CRUD operations'
      case 'debts':
        return 'Track customer debt and payments'
      case 'family-gallery':
        return 'Manage family photos and memories'
      case 'api-graphing':
        return 'Visual analytics and business insights'
      case 'history':
        return 'View transaction and activity history'
      case 'calendar':
        return 'Manage events and schedules'
      case 'settings':
        return 'Configure your store settings'
      case 'ai-support':
        return 'Get intelligent assistance for your store management'
      default:
        return 'Overview of your Revantad Store'
    }
  }

  return (
    <ProtectedRoute>
      <div
        className="min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#f9fafb'
        }}
      >
        {/* Facebook-style Header */}
        <AdminHeader
          activeSection={activeSection}
          setActiveSection={setActiveSection}
        />

        <div className="flex pt-16">
          {/* Updated Sidebar */}
          <Sidebar activeSection={activeSection} setActiveSection={setActiveSection} />

          {/* Main Content */}
          <main
            className="flex-1 transition-colors duration-300 main-content-scroll"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#ffffff',
              height: 'calc(100vh - 4rem)',
              overflowY: 'auto',
              overflowX: 'hidden'
            }}
          >
            <div className="p-8">
              <div className="mb-8">
                <h1
                  className="text-3xl font-bold transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937'
                  }}
                >
                  {getPageTitle()}
                </h1>
                <p
                  className="mt-2 transition-colors duration-300"
                  style={{
                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                  }}
                >
                  {getPageDescription()}
                </p>
              </div>
              {renderContent()}
            </div>
          </main>
        </div>

        {/* AI Assistant */}
        <AIAssistant context={activeSection} />
      </div>
    </ProtectedRoute>
  )
}
