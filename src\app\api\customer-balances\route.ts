import { NextRequest, NextResponse } from 'next/server'

import {
  successResponse,
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  handleDatabaseError,
  parsePaginationParams,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all customer balances with pagination
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const { page, limit, offset } = parsePaginationParams(searchParams)

  // Optional filters
  const search = searchParams.get('search')
  const hasBalance = searchParams.get('hasBalance') // 'true' to show only customers with remaining balance

  let query = supabase
    .from('customer_balances')
    .select('*', { count: 'exact' })
    .order('remaining_balance', { ascending: false })
    .range(offset, offset + limit - 1)

  // Apply filters
  if (search) {
    query = query.or(`customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%`)
  }

  if (hasBalance === 'true') {
    query = query.gt('remaining_balance', 0)
  }

  const { data: balances, error, count } = await query

  if (error) {
    return handleDatabaseError(error)
  }

  return successResponse({
    balances,
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  })
})

// GET specific customer balance
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { customer_name, customer_family_name } = body

    if (!customer_name || !customer_family_name) {
      return NextResponse.json(
        { error: 'Customer name and family name are required' },
        { status: 400 }
      )
    }

    const { data: balance, error } = await supabase
      .from('customer_balances')
      .select('*')
      .eq('customer_name', customer_name)
      .eq('customer_family_name', customer_family_name)
      .single()

    if (error) {
      // If no balance found, return zero balance
      if (error.code === 'PGRST116') {
        return NextResponse.json({
          balance: {
            customer_name,
            customer_family_name,
            total_debt: 0,
            total_payments: 0,
            remaining_balance: 0,
            last_debt_date: null,
            last_payment_date: null
          }
        })
      }
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ balance })
  } catch (error) {
    console.error('Error fetching customer balance:', error)
    return NextResponse.json(
      { error: 'Failed to fetch customer balance' },
      { status: 500 }
    )
  }
}
