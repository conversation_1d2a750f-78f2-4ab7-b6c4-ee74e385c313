import { NextRequest, NextResponse } from 'next/server'

import {
  successResponse,
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  handleDatabaseError,
  parsePaginationParams,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all customers with pagination
export const GET = withError<PERSON>andler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const { page, limit, offset } = parsePaginationParams(searchParams)

  // Optional filters
  const search = searchParams.get('search')

  let query = supabase
    .from('customers')
    .select('*', { count: 'exact' })
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1)

  // Apply search filter
  if (search) {
    query = query.or(`customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%,phone_number.ilike.%${search}%`)
  }

  const { data: customers, error, count } = await query

  if (error) {
    return handleDatabaseError(error)
  }

  return successResponse({
    customers,
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  })
})

// POST - Create new customer or update existing
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      customer_name,
      customer_family_name,
      profile_picture_url,
      profile_picture_public_id,
      phone_number,
      address,
      notes,
    } = body

    // Validate required fields
    if (!customer_name || !customer_family_name) {
      return NextResponse.json(
        { error: 'Customer name and family name are required' },
        { status: 400 }
      )
    }

    // Check if customer already exists
    const { data: existingCustomer } = await supabase
      .from('customers')
      .select('id')
      .eq('customer_name', customer_name)
      .eq('customer_family_name', customer_family_name)
      .single()

    if (existingCustomer) {
      // Update existing customer
      const { data: customer, error } = await supabase
        .from('customers')
        .update({
          profile_picture_url: profile_picture_url || null,
          profile_picture_public_id: profile_picture_public_id || null,
          phone_number: phone_number || null,
          address: address || null,
          notes: notes || null,
        })
        .eq('id', existingCustomer.id)
        .select()
        .single()

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      return NextResponse.json({ customer }, { status: 200 })
    } else {
      // Create new customer
      const { data: customer, error } = await supabase
        .from('customers')
        .insert([
          {
            customer_name,
            customer_family_name,
            profile_picture_url: profile_picture_url || null,
            profile_picture_public_id: profile_picture_public_id || null,
            phone_number: phone_number || null,
            address: address || null,
            notes: notes || null,
          },
        ])
        .select()
        .single()

      if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      return NextResponse.json({ customer }, { status: 201 })
    }
  } catch (error) {
    console.error('Error managing customer:', error)
    return NextResponse.json(
      { error: 'Failed to manage customer record' },
      { status: 500 }
    )
  }
}
