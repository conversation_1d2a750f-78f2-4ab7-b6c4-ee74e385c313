import { NextRequest, NextResponse } from 'next/server'

import { supabase } from '@/lib/supabase'

// GET - Fetch single customer payment
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { data: payment, error } = await supabase
      .from('customer_payments')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!payment) {
      return NextResponse.json({ error: 'Payment record not found' }, { status: 404 })
    }

    return NextResponse.json({ payment })
  } catch {
    return NextResponse.json(
      { error: 'Failed to fetch payment record' },
      { status: 500 }
    )
  }
}

// PUT - Update customer payment
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const {
      customer_name,
      customer_family_name,
      payment_amount,
      payment_date,
      payment_method,
      notes,
    } = body

    // Validate required fields
    if (
      !customer_name ||
      !customer_family_name ||
      !payment_amount ||
      payment_amount <= 0
    ) {
      return NextResponse.json(
        { error: 'Missing required fields or invalid payment amount' },
        { status: 400 }
      )
    }

    const { data: payment, error } = await supabase
      .from('customer_payments')
      .update({
        customer_name,
        customer_family_name,
        payment_amount: parseFloat(payment_amount),
        payment_date: payment_date || new Date().toISOString().split('T')[0],
        payment_method: payment_method || 'Cash',
        notes: notes || null,
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ payment })
  } catch {
    return NextResponse.json(
      { error: 'Failed to update payment record' },
      { status: 500 }
    )
  }
}

// DELETE - Delete customer payment
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { error } = await supabase
      .from('customer_payments')
      .delete()
      .eq('id', id)

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ message: 'Payment record deleted successfully' })
  } catch {
    return NextResponse.json(
      { error: 'Failed to delete payment record' },
      { status: 500 }
    )
  }
}
