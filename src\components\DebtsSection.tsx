'use client'

import { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Search, Users, Calendar } from 'lucide-react'
import { useTheme } from 'next-themes'
import DebtModal from './DebtModal'
import LoadingSkeleton from './LoadingSkeleton'
import { CustomerDebt } from '@/lib/supabase'
import { format } from 'date-fns'

interface DebtsSectionProps {
  onStatsUpdate: () => void
}

export default function DebtsSection({ onStatsUpdate }: DebtsSectionProps) {
  const { resolvedTheme } = useTheme()
  const [debts, setDebts] = useState<CustomerDebt[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingDebt, setEditingDebt] = useState<CustomerDebt | null>(null)

  useEffect(() => {
    fetchDebts()
  }, [])

  const fetchDebts = async () => {
    try {
      console.log('🔄 Fetching debts...')
      const response = await fetch('/api/debts')
      const data = await response.json()
      console.log('💳 Debts API response:', data)

      if (data.success && data.data && data.data.debts) {
        setDebts(data.data.debts)
        console.log('✅ Debts loaded:', data.data.debts.length, 'items')
      } else {
        setDebts(data.debts || [])
        console.log('⚠️ Using fallback debts structure')
      }
    } catch (error) {
      console.error('❌ Error fetching debts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this debt record?')) return

    try {
      const response = await fetch(`/api/debts/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setDebts(debts.filter(d => d.id !== id))
        onStatsUpdate()
      }
    } catch (error) {
      console.error('Error deleting debt:', error)
    }
  }

  const handleEdit = (debt: CustomerDebt) => {
    setEditingDebt(debt)
    setIsModalOpen(true)
  }

  const handleModalClose = () => {
    setIsModalOpen(false)
    setEditingDebt(null)
    fetchDebts()
    onStatsUpdate()
  }

  const filteredDebts = debts.filter(debt => {
    const customerName = `${debt.customer_name} ${debt.customer_family_name}`.toLowerCase()
    const productName = debt.product_name.toLowerCase()
    const search = searchTerm.toLowerCase()
    return customerName.includes(search) || productName.includes(search)
  })

  // Group debts by customer
  const groupedDebts = filteredDebts.reduce((acc, debt) => {
    const customerKey = `${debt.customer_name} ${debt.customer_family_name}`
    if (!acc[customerKey]) {
      acc[customerKey] = []
    }
    acc[customerKey].push(debt)
    return acc
  }, {} as Record<string, CustomerDebt[]>)

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex justify-between items-center">
          <div
            className="w-64 h-10 rounded-lg"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',
              backgroundImage: resolvedTheme === 'dark'
                ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'
                : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',
              backgroundSize: '200% 100%',
              animation: 'shimmer 2s infinite'
            }}
          />
          <div
            className="w-48 h-10 rounded-lg"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f3f4f6',
              backgroundImage: resolvedTheme === 'dark'
                ? 'linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%)'
                : 'linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%)',
              backgroundSize: '200% 100%',
              animation: 'shimmer 2s infinite'
            }}
          />
        </div>

        {/* Debts List Skeleton */}
        <LoadingSkeleton type="debts" count={4} />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="relative">
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300"
            style={{
              color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
            }}
          />
          <input
            type="text"
            placeholder="Search by customer or product..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
              border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
              color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
            }}
          />
        </div>
        <button
          onClick={() => setIsModalOpen(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Debt Record
        </button>
      </div>

      {/* Debts List */}
      <div className="space-y-6">
        {Object.entries(groupedDebts).map(([customerName, customerDebts]) => {
          const totalAmount = customerDebts.reduce((sum, debt) => sum + debt.total_amount, 0)
          
          return (
            <div key={customerName} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-gray-400 mr-2" />
                    <h3 className="text-lg font-semibold text-gray-900">{customerName}</h3>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">{customerDebts.length} item(s)</p>
                    <p className="text-lg font-bold text-red-600">₱{totalAmount.toFixed(2)}</p>
                  </div>
                </div>
              </div>
              
              <div className="divide-y divide-gray-200">
                {customerDebts.map((debt) => (
                  <div key={debt.id} className="px-6 py-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{debt.product_name}</h4>
                        <div className="mt-1 text-sm text-gray-600 space-y-1">
                          <div className="flex items-center">
                            <span>Quantity: {debt.quantity}</span>
                            <span className="mx-2">•</span>
                            <span>Unit Price: ₱{debt.product_price.toFixed(2)}</span>
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            <span>Date: {format(new Date(debt.debt_date), 'MMM dd, yyyy')}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <div className="text-right">
                          <p className="font-semibold text-gray-900">₱{debt.total_amount.toFixed(2)}</p>
                        </div>
                        <button
                          onClick={() => handleEdit(debt)}
                          className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(debt.id)}
                          className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        })}
      </div>

      {filteredDebts.length === 0 && !loading && (
        <div
          className="text-center py-16 rounded-xl border-2 border-dashed transition-all duration-300"
          style={{
            backgroundColor: resolvedTheme === 'dark' ? 'rgba(30, 41, 59, 0.5)' : 'rgba(249, 250, 251, 0.8)',
            borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'
          }}
        >
          <div
            className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 transition-all duration-300"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(34, 197, 94, 0.05)',
              border: resolvedTheme === 'dark' ? '2px solid rgba(34, 197, 94, 0.3)' : '2px solid rgba(34, 197, 94, 0.2)'
            }}
          >
            <Users
              className="h-10 w-10 transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#4ade80' : '#16a34a'
              }}
            />
          </div>
          <h3
            className="text-xl font-semibold mb-3 transition-colors duration-300"
            style={{
              color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
            }}
          >
            {searchTerm ? 'No debt records found' : 'No debt records yet'}
          </h3>
          <p
            className="text-sm mb-6 max-w-md mx-auto transition-colors duration-300"
            style={{
              color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280'
            }}
          >
            {searchTerm
              ? 'Try adjusting your search criteria to find specific customers or products'
              : 'Start tracking customer debts by adding your first debt record'}
          </p>
          {!searchTerm && (
            <button
              onClick={() => setIsModalOpen(true)}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl"
            >
              <Plus className="h-5 w-5 mr-2" />
              Add First Debt Record
            </button>
          )}
        </div>
      )}

      {/* Debt Modal */}
      <DebtModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        debt={editingDebt}
      />
    </div>
  )
}
