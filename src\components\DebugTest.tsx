'use client'

import { useState, useEffect } from 'react'

export default function DebugTest() {
  const [products, setProducts] = useState([])
  const [debts, setDebts] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const testAPIs = async () => {
      try {
        console.log('🧪 Testing APIs...')
        
        // Test products API
        const productsResponse = await fetch('/api/products')
        const productsData = await productsResponse.json()
        console.log('📦 Products API Response:', productsData)
        
        if (productsData.success && productsData.data && productsData.data.products) {
          setProducts(productsData.data.products)
          console.log('✅ Products set:', productsData.data.products.length, 'items')
        }
        
        // Test debts API
        const debtsResponse = await fetch('/api/debts')
        const debtsData = await debtsResponse.json()
        console.log('💳 Debts API Response:', debtsData)
        
        if (debtsData.success && debtsData.data && debtsData.data.debts) {
          setDebts(debtsData.data.debts)
          console.log('✅ Debts set:', debtsData.data.debts.length, 'items')
        }
        
      } catch (error) {
        console.error('❌ API Test Error:', error)
      } finally {
        setLoading(false)
      }
    }

    testAPIs()
  }, [])

  if (loading) {
    return <div className="p-4">🔄 Testing APIs...</div>
  }

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">🧪 API Debug Test</h2>
      
      <div className="bg-blue-50 p-4 rounded">
        <h3 className="font-semibold">📦 Products ({products.length})</h3>
        {products.length > 0 ? (
          <ul className="mt-2">
            {products.slice(0, 3).map((product: any) => (
              <li key={product.id} className="text-sm">
                • {product.name} - ₱{product.price}
              </li>
            ))}
            {products.length > 3 && <li className="text-sm text-gray-500">... and {products.length - 3} more</li>}
          </ul>
        ) : (
          <p className="text-red-500">No products found</p>
        )}
      </div>
      
      <div className="bg-green-50 p-4 rounded">
        <h3 className="font-semibold">💳 Debts ({debts.length})</h3>
        {debts.length > 0 ? (
          <ul className="mt-2">
            {debts.slice(0, 3).map((debt: any) => (
              <li key={debt.id} className="text-sm">
                • {debt.customer_name} {debt.customer_family_name} - {debt.product_name} (₱{debt.total_amount})
              </li>
            ))}
            {debts.length > 3 && <li className="text-sm text-gray-500">... and {debts.length - 3} more</li>}
          </ul>
        ) : (
          <p className="text-red-500">No debts found</p>
        )}
      </div>
    </div>
  )
}
