'use client'

import { useState, useRef, useEffect } from 'react'
import { 
  MoreVertical, Edit, Trash2, Eye, Copy, Star, 
  TrendingUp, Package, Zap, Download 
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { Product } from '@/lib/supabase'

interface ProductQuickActionsProps {
  product: Product
  onEdit: (product: Product) => void
  onDelete: (id: string) => void
  onDuplicate?: (product: Product) => void
  onToggleFavorite?: (id: string) => void
  className?: string
}

export default function ProductQuickActions({
  product,
  onEdit,
  onDelete,
  onDuplicate,
  onToggleFavorite,
  className = ''
}: ProductQuickActionsProps) {
  const { resolvedTheme } = useTheme()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleAction = (action: () => void) => {
    action()
    setIsOpen(false)
  }

  const quickActions = [
    {
      label: 'Edit Product',
      icon: Edit,
      action: () => onEdit(product),
      color: 'text-blue-600'
    },
    {
      label: 'View Details',
      icon: Eye,
      action: () => console.log('View details:', product.id),
      color: 'text-gray-600'
    },
    {
      label: 'Duplicate',
      icon: Copy,
      action: () => onDuplicate?.(product),
      color: 'text-green-600'
    },
    {
      label: 'Add to Favorites',
      icon: Star,
      action: () => onToggleFavorite?.(product.id),
      color: 'text-yellow-600'
    },
    {
      label: 'View Analytics',
      icon: TrendingUp,
      action: () => console.log('View analytics:', product.id),
      color: 'text-purple-600'
    },
    {
      label: 'Export Data',
      icon: Download,
      action: () => console.log('Export data:', product.id),
      color: 'text-indigo-600'
    },
    {
      label: 'Delete Product',
      icon: Trash2,
      action: () => onDelete(product.id),
      color: 'text-red-600',
      separator: true
    }
  ]

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-1.5 rounded-md bg-white/90 text-gray-600 hover:bg-white shadow-sm transition-all duration-200 hover:shadow-md"
        title="Quick Actions"
      >
        <MoreVertical className="h-4 w-4" />
      </button>

      {isOpen && (
        <div 
          className="absolute right-0 top-full mt-1 w-48 rounded-lg shadow-lg border z-50 py-1 animate-slide-down"
          style={{
            backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
            border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #e5e7eb'
          }}
        >
          {quickActions.map((action, index) => (
            <div key={index}>
              {action.separator && (
                <div 
                  className="my-1 h-px"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#6b7280' : '#e5e7eb'
                  }}
                />
              )}
              <button
                onClick={() => handleAction(action.action)}
                className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-3"
                style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                <action.icon className={`h-4 w-4 ${action.color}`} />
                <span className="text-sm font-medium">{action.label}</span>
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
